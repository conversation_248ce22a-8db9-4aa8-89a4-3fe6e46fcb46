import { FormController } from "@web/views/form/form_controller";
import { pickUseConnectedEmployee } from "../employee_selection/employee_hooks";
import { useService } from "@web/core/utils/hooks";
import {
    onWillStart,
    useSubEnv,
} from "@odoo/owl";

export class SaleFormController extends FormController {
    setup() {
        super.setup();
        console.log("Sale Form Controller setup");

        this.actionService = useService('action');
        this.notification = useService('notification');
        onWillStart(async () => {
            try {
                // must check to know if employee already login
                var emp_login = false
                const order_action = await this.actionService.loadAction('sale.action_orders');
                const quotation_action = await this.actionService.loadAction('sale.action_quotations_with_onboarding');
                // await this.useEmployee.getAllEmployees();
                await this.useEmployee.getConnectedEmployees();
                if (([order_action.id, quotation_action.id].includes(this.env.config.actionId) && !this.props.resId)) {
                    this.useEmployee.popupAddEmployee('add_employee');
                }
            } catch (error) {
                // Handle component destruction gracefully
                if (error.message && error.message.includes("Component is destroyed")) {
                    console.log("Component destroyed during controller setup, skipping initialization");
                    return;
                }
                // Handle other errors
                console.error("Error in SaleFormController onWillStart:", error);
                // Continue with setup even if employee loading fails
                this.notification.add("Warning: Employee data could not be loaded", { type: "warning" });
            }
        });
        useSubEnv({
            model: this.model,
            reload: async () => {
                try {
                    await this.model.load();
                    await this.useEmployee.getConnectedEmployees();
                } catch (error) {
                    // Handle component destruction gracefully
                    if (error.message && error.message.includes("Component is destroyed")) {
                        console.log("Component destroyed during reload, skipping");
                        return;
                    }
                    // Handle other errors
                    console.error("Error in SaleFormController reload:", error);
                    // Continue with model load even if employee loading fails
                    try {
                        await this.model.load();
                    } catch (modelError) {
                        console.error("Error loading model:", modelError);
                    }
                }
            },
        });
        this.useEmployee = pickUseConnectedEmployee("form", this.props.context, this.env);

        this.useEmployee.setFormSaveCallbacks({
            refreshForm: this.refreshForm.bind(this),
            getSaleOrderId: () => this.props.resId
        });
    }
    async create() {
        try {
            if (this.props.resModel === 'sale.order') {
                this.useEmployee.popupAddEmployee('add_employee');
            }
            super.create();
        } catch (error) {
            // Handle component destruction gracefully
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during create, skipping");
                return;
            }
            // Handle other errors
            console.error("Error in SaleFormController create:", error);
            // Continue with create even if employee popup fails
            super.create();
        }
    }
    async refreshForm() {
        try {
            this.render();

        } catch (error) {
            this.notification.add("Error refreshing form", { type: "danger" });
            console.error("Form refresh error:", error);
        }
    }
}
