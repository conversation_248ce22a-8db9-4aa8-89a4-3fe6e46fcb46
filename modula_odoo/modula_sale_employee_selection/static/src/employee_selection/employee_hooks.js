import { _t } from "@web/core/l10n/translation";
import { useService } from "@web/core/utils/hooks";
import { browser } from "@web/core/browser/browser";
import { SelectionPopup } from "./popup";
import { PinPopup } from "./pin_popup";
import { DialogWrapper } from "./dialog_wrapper";
import { registry } from "@web/core/registry";
import { useState } from "@odoo/owl";
import { startServices } from "@web/env";


export function pickUseConnectedEmployee(controllerType, initialContext, workcenterId, env) {

    const orm = useService("orm");
    const notification = useService("notification");
    const dialog = useService("dialog");
    const imageBaseURL = `${browser.location.origin}/web/image?model=hr.employee&field=avatar_128&id=`;

    let formSaveCallbacks = {};
    let pinWasOpened = false;
    let employeeWasSelected = false;
    let context = initialContext || {};


    const employees = useState({
        connected: [],
        all: [],
        admin: {},
    });

    const floorTypes = useState({
        connected: [],
        all: [],
        admin: {},
    });
    const popup = useState({
        PinPopup: {
            isShown: false,
        },
        SelectionPopup: {
            isShown: false,
        },
    });

    const openDialog = (id, component, props) => {
        popup[id] = {
            isShown: true,
            close: dialog.add(
                DialogWrapper,
                {
                    Component: component,
                    componentProps: { ...props, action: props.action },
                },
                {
                    onClose: () => {
                        popup[id] = { isShown: false };
                    },
                }
            ),
        };
    };

    const setFormSaveCallbacks = (callbacks) => {
        formSaveCallbacks = callbacks;
    };

    const getAllEmployees = async (model = null, id = null) => {
        try {
            if (model && id) {
                employees.res_model = model;
                employees.res_id = id;
            }
            const res = await orm.call("hr.employee", "get_all_employees", [false]);
            if (res.all) {
                employees.all = res.all;
            } else {
                employees.all = [];
            }
        } catch (error) {
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during getAllEmployees, skipping update");
                return;
            }
            console.error("Error in getAllEmployees:", error);
            employees.all = employees.all || [];
        }
    };

    const filterEmployeesByIsShow = () => {
        if (employees.all && Array.isArray(employees.all)) {
            // Filter employees with is_show = true
            employees.all = employees.all.filter(employee => employee.is_show === true);

            // Add default action_approve_sale_order field to each employee
            employees.all = employees.all.map(employee => ({
                ...employee,
                action_approve_sale_order: true  // Default value for approval action
            }));

            console.log(`Filtered employees: ${employees.all.length} employees with is_show=true and action_approve_sale_order=true`);
        }
    };

    // Add context to call in back-end, no other way to pass context to back-end
    // this should keep at all cost, please do not remove it.
    const getEmployeeContext = (employeeId) => {
        const employee = employees.all.find((e) => e.id === employeeId);
        return {
            action_approve_sale_order: employee && employee.action_approve_sale_order || false,
            res_model: employees.res_model || null,
            res_id: employees.res_id || null,
            action_validate_with_employee_selection: context && context.action_validate_with_employee_selection || false,
        };
    };

    // const getAllFloorTypes = async () => {
    //     const fieldsToRead = ["id", "name"];
    //     floorTypes.all = await orm.searchRead("flooring.type", [], fieldsToRead);
    // };

    const hideApproveButton = async () => {
        try {
            const approveButtons = document.getElementsByName('action_approve_sale_order');

            if (approveButtons.length > 0) {
                for (let i = 0; i < approveButtons.length; i++) {
                    approveButtons[i].style.display = 'none';
                }
                console.log("Approve button(s) hidden successfully");
            } else {
                console.warn("No Approve button found with name 'action_approve_sale_order'");
            }

        } catch (error) {
            console.error("Error hiding approve button:", error);
        }
    };

    const showApproveButton = async () => {
        try {
            // Find the "Approve" button by name attribute
            const approveButtons = document.getElementsByName('action_approve_sale_order');

            if (approveButtons.length > 0) {
                // Show all "Approve" buttons found
                for (let i = 0; i < approveButtons.length; i++) {
                    approveButtons[i].style.display = '';
                }
                console.log("Approve button(s) shown successfully");
            } else {
                console.warn("No Approve button found with name 'action_approve_sale_order'");
            }

        } catch (error) {
            console.error("Error showing approve button:", error);
        }
    };

    const selectEmployee = async (employeeId, pin) => {
        try {
            const employee = employees.all.find((e) => e.id === employeeId);
            const employee_connected = employees.connected.find((e) => e.name && e.id === employee.id);
            const employee_function = employee_connected ? "logout" : "login";

            const context = getEmployeeContext(employeeId);

            console.log(`Selecting employee ${employeeId} for ${context.res_model} ID: ${context.res_id}`);

            const loginResult = await orm.call("hr.employee", employee_function, [employeeId, pin], { context });

        let pinValid = false;
        let wizardAction = null;
        let shouldRefreshForm = false;

        if (typeof loginResult === 'boolean') {
            pinValid = loginResult;
            shouldRefreshForm = pinValid && context.res_model === 'stock.picking';
        } else if (typeof loginResult === 'object' && loginResult !== null) {
            pinValid = loginResult.success || false;
            wizardAction = loginResult.wizard_action || null;
            shouldRefreshForm = loginResult.refresh_form || false;

            if (!pinValid && loginResult.error) {
                console.error("Employee login error:", loginResult.error);
                notification.add(loginResult.message || _t("Login failed"), { type: "danger" });
                return;
            }
        }

        if (!pinValid && popup.PinPopup.isShown) {
            return notification.add(_t("Wrong password!"), { type: "danger" });
        }
        if (!pinValid) {
            return askPin(employee);
        }

        employeeWasSelected = true;

        if (!pinWasOpened) {
            await hideApproveButton();
        }

        if (employee_function === "login") {
            notification.add(_t("Success!"), { type: "success" });
            await getConnectedEmployees(true);

            if (context.res_model === 'stock.picking') {
                if (wizardAction) {
                    console.log("wizard action for stock picking:", wizardAction);
                    try {
                        if (formSaveCallbacks && formSaveCallbacks.executeWizardAction) {
                            await formSaveCallbacks.executeWizardAction(wizardAction);
                        }
                    } catch (error) {
                        notification.add(_t("Error executing validation wizard"), { type: "danger" });
                    }
                } else if (shouldRefreshForm) {
                    if (formSaveCallbacks && formSaveCallbacks.refreshForm) {
                        try {
                            await formSaveCallbacks.refreshForm();
                        } catch (error) {
                            console.error("Error refreshing stock picking form:", error);
                            notification.add(_t("Error refreshing form"), { type: "danger" });
                        }
                    }
                }
            } else if (context.res_model === 'sale.order') {
                // Check if this is a discount application workflow
                if (context.action_validate_with_employee_selection && formSaveCallbacks && formSaveCallbacks.applyDiscount) {
                    console.log("applying discount with employee validation");
                    await formSaveCallbacks.applyDiscount();
                } else if (formSaveCallbacks && formSaveCallbacks.saveForm) {
                    console.log("saving sale order form");
                    await formSaveCallbacks.saveForm();
                }
            }

            if (controllerType === "kanban") {
                console.log('thing we must do after employee connected login');
            }
        } else {
            // await stopAllWorkorderFromEmployee(employeeId);
            notification.add(_t("Logged out!"), { type: "success" });
        }

            closePopup("SelectionPopup");
            await getConnectedEmployees();
        } catch (error) {
            // Handle component destruction gracefully
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during selectEmployee, skipping update");
                closePopup("SelectionPopup");
                return;
            }
            console.error("Error in selectEmployee:", error);
            notification.add(_t("Error selecting employee"), { type: "danger" });
            closePopup("SelectionPopup");
        }
    };

    const getConnectedEmployees = async (login = false) => {
        try {
            const res = await orm.call("hr.employee", "get_all_employees", [null, login]);
            if (res.all) {
                employees.all = res.all;
            } else {
                employees.all = [];
            }
            res.connected.sort(function (emp1, emp2) {
                if (emp1.workorder.length == 0) {
                    return 1;
                }
                if (emp2.workorder.length == 0) {
                    return -1;
                }
                return 0;
            });
            employees.connected = res.connected.map((obj) => {
                const emp = employees.all.find(e => e.id === obj.id);
                return { ...obj, name: emp.name };
            })
            const admin = employees.all.find(e => e.id === res.admin);
            if (admin) {
                employees.admin = {
                    name: admin.name,
                    id: admin.id,
                    path: imageBaseURL + `${admin.id}`,
                }
            } else {
                employees.admin = {};
            }
        } catch (error) {
            // Handle component destruction gracefully
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during getConnectedEmployees, skipping update");
                return;
            }
            console.error("Error in getConnectedEmployees:", error);
            employees.all = employees.all || [];
            employees.connected = employees.connected || [];
            employees.admin = employees.admin || {};
        }
    };

    // const getConnectedFlooring = async () => {
    //     const res = await orm.call("flooring.type", "get_all_flooring", [null]);
    //     floorTypes.all = res.all;

    //     floorTypes.connected = res.connected.map((obj) => {
    //         const floor = floorTypes.all.find(f => f.id === obj.id);
    //         return { ...obj, name: floor.name };
    //     })
    // };

    const logout = async (employeeId) => {
        try {
            const context = getEmployeeContext(employeeId);
            const success = await orm.call("hr.employee", "logout", [employeeId, false, true], { context });
            if (success) {
                notification.add(_t("Logged out!"), { type: "success" });
                // await Promise.all([stopAllWorkorderFromEmployee(employeeId), getConnectedEmployees()]);
            } else {
                notification.add(_t("Error during log out!"), { type: "danger" });
            }
        } catch (error) {
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during logout, skipping update");
                return;
            }
            console.error("Error in logout:", error);
            notification.add(_t("Error during log out!"), { type: "danger" });
        }
    };

    const askPin = async (employee) => {
        try {
            pinWasOpened = true;

            openDialog("PinPopup", PinPopup, {
                popupData: { employee },
                onClosePopup: async (popupId) => {
                    closePopup(popupId);

                    if (employeeWasSelected) {
                        await hideApproveButton();

                        // Stock picking form refresh after confirm PIN validation
                        const context = getEmployeeContext(employee.id);
                        if (context.res_model === 'stock.picking' && formSaveCallbacks) {
                            try {
                                if (formSaveCallbacks.refreshForm) {
                                    await formSaveCallbacks.refreshForm();
                                }
                            } catch (error) {
                                console.error("Error refreshing form after PIN validation:", error);
                                notification.add(_t("Error refreshing form"), { type: "danger" });
                            }
                        }

                        employeeWasSelected = false; // Reset flag
                    }
                },
                onPinValidate: checkPin.bind(this),
            });
        } catch (error) {
            console.error("Error in askPin:", error);
            notification.add(_t("Error opening PIN validation"), { type: "danger" });
        }
    };

    const toggleSessionOwner = async (employee_id, pin) => {
        try {
            if (employees.admin.id == employee_id) {
                const context = getEmployeeContext(employee_id);
                await orm.call("hr.employee", "remove_session_owner", [employee_id], { context });
                await getConnectedEmployees();
            } else {
                setSessionOwner(employee_id, pin);
            }
        } catch (error) {
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during toggleSessionOwner, skipping update");
                return;
            }
            console.error("Error in toggleSessionOwner:", error);
            notification.add(_t("Error toggling session owner"), { type: "danger" });
        }
    };

    const setSessionOwner = async (employee_id, pin) => {
        try {
            if (employees.admin.id == employee_id && employee_id == employees.connected[0].id) {
                return;
            }
            const context = getEmployeeContext(employee_id);
            const pinValid = await orm.call("hr.employee", "login", [employee_id, pin], { context });

            if (!pinValid) {
                if (pin) {
                    notification.add(_t("Wrong password!"), { type: "danger" });
                }
                if (popup.PinPopup.isShown) {
                    return;
                }
                askPin({ id: employee_id });
            }
            await getConnectedEmployees();
        } catch (error) {
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during setSessionOwner, skipping update");
                return;
            }
            console.error("Error in setSessionOwner:", error);
            notification.add(_t("Error setting session owner"), { type: "danger" });
        }
    };

    // const stopAllWorkorderFromEmployee = async (employeeId) => {
    //     await orm.call("hr.employee", "stop_all_workorder_from_employee", [employeeId]);
    // };

    const popupAddEmployee = async (action = null) => {
        try {
            pinWasOpened = false;
            employeeWasSelected = false;

            const list = employees.all.map((employee) =>
                Object.create({
                    id: employee.id,
                    item: employee,
                    label: employee.name,
                    barcode: employee.barcode,
                    isSelected: employees.connected.find((e) => e.id === employee.id),
                })
            );
            openDialog("SelectionPopup", SelectionPopup, {
                action: action,
                popupData: { title: _t("Select Employee"), list: list },
                onClosePopup: async (popupId) => {
                    closePopup(popupId);

                    employeeWasSelected = false;
                },
                onSelectEmployee: selectEmployee.bind(this),
            });
        } catch (error) {
            console.error("Error in popupAddEmployee:", error);
            notification.add(_t("Error opening employee selection"), { type: "danger" });
        }
    };

    const pinValidation = async (employeeId, pin) => {
        try {
            const context = getEmployeeContext(employeeId);
            return await orm.call("hr.employee", "pin_validation", [employeeId, pin], { context });
        } catch (error) {
            // Handle component destruction gracefully
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during pinValidation, returning false");
                return false;
            }
            // Handle other errors
            console.error("Error in pinValidation:", error);
            return false;
        }
    };

    const checkPin = async (employeeId, pin) => {
        try {
            if (
                employees.connected.find((e) => e.id === employeeId) &&
                employees.admin?.id != employeeId
            ) {
                setSessionOwner(employeeId, pin);
            } else {
                selectEmployee(employeeId, pin);
            }
            const context = getEmployeeContext(employeeId);
            const pinValid = await orm.call("hr.employee", "pin_validation", [employeeId, pin], { context });
            return pinValid;
        } catch (error) {
            if (error.message && error.message.includes("Component is destroyed")) {
                console.log("Component destroyed during checkPin, returning false");
                return false;
            }
            console.error("Error in checkPin:", error);
            return false;
        }
    };

    const closePopup = (popupId) => {
        const { isShown, close } = popup[popupId];
        if (isShown && close) {
            close();
            popup[popupId] = { isShown: false };
        }
    };

    const openRecord = async (record, mode) => {
        const id = await orm.call("hr.employee", "get_session_owner", [null]);
        if (id.length == 0) {
            context.openRecord = [record, mode];
            // openEmployeeSelection();
            return;
        }
        delete context.openRecord;
        Object.assign(context, { employees: id });
    };

    return {
        getAllEmployees,
        // getAllFloorTypes,
        getConnectedEmployees,
        // getConnectedFlooring,
        logout,
        askPin,
        setSessionOwner,
        // stopAllWorkorderFromEmployee,
        toggleSessionOwner,
        popupAddEmployee,
        // popupAddFlooringType,
        checkPin,
        closePopup,
        pinValidation,
        selectEmployee,
        openRecord,
        employees,
        popup,
        setFormSaveCallbacks,
        filterEmployeesByIsShow,
    };
}

export async function ActionValidateWithEmployeeSelection(env, action) {
    console.log("action_validate_with_employee_selection triggered", action);

    const validateEmployeeWithPin = async (employeeId, pinCode, action) => {
        // Create context based on model type
        const context = action.params.model === "sale.order" ? {
            action_approve_sale_order: true,
            res_model: 'sale.order',
            res_id: action.params.sale_order_id,
            action_validate_with_employee_selection: true
        } : {
            res_model: 'stock.picking',
            res_id: action.params.picking_id,
            action_validate_with_employee_selection: true
        };

        const loginResult = await env.services.orm.call(
            "hr.employee",
            "login",
            [employeeId, pinCode],
            { context }
        );

        if (loginResult) {
            // Handle successful login based on model type
            if (action.params.model === "sale.order") {
                await tryToValidateSaleOrder(action);
            } else if (action.params.model === "stock.picking") {
                await tryToValidateStockPicking(action);
            }
            return true;
        } else {
            env.services.notification.add(_t("Wrong password!"), { type: "danger" });
            return false;
        }
    };

    const tryToValidateSaleOrder = async (action) => {
        const context_params = {
            discount_type: action.params.discount_type,
            discount_percentage: action.params.discount_percentage,
            discount_amount: action.params.discount_amount,
        }
        await env.services.orm.call(
            "sale.order",
            "action_employee_validation_for_sale_order",
            [action.params.sale_order_id],
            {
                context: context_params
            }
        ).then(result => {
            if (result) {
                env.services.dialog.closeAll();
                env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            }
        });
    }

    const tryToValidateStockPicking = async (action) => {
        try {
            env.services.dialog.closeAll();

            const result = await env.services.orm.call(
                "stock.picking",
                "action_employee_validation_for_stock_picking",
                [action.params.picking_id]
            );

            if (result) {
                return await handleStockPickingValidation(result, action);
            }
        } catch (error) {
            console.error("Error in stock picking validation:", error);
            env.services.dialog.closeAll();
            env.services.notification.add(_t("Error validating picking: ") + error.message, { type: "danger" });
        }
    }
    const handleStockPickingValidation = async (result, action) => {
        if (result) {
            if (result && typeof result === 'object' && result.wizard_action && result.success === true) {
                // Close employee selection dialogs first
                console.log("Opening backorder wizard:", result.wizard_action);
                const wizard_result = await env.services.action.doAction(result.wizard_action, {
                    onClose: () => handleStockPickingOnClose(result, action)
                });
                return;
            } else if (typeof result === 'boolean' && result === true) {
                env.services.dialog.closeAll();
                env.services.notification.add(_t("Picking validated successfully"), { type: "success" });
                await env.services.action.doAction({
                    type: "ir.actions.act_window",
                    res_model: "stock.picking",
                    res_id: action.params.picking_id,
                    views: [[false, "form"]],
                    target: "current"
                });
                return;
            } else if (typeof result === 'object' && result.success === false) {
                // Backend returned error object
                env.services.dialog.closeAll();
                env.services.notification.add(_t(result.message || "Validation failed"), { type: "danger" });
                return;
            } else {
                // Unexpected result type
                env.services.dialog.closeAll();
                env.services.notification.add(_t("Unexpected validation result"), { type: "warning" });
                console.warn("Unexpected result from stock picking validation:", result);
                return;
            }
        } else {
            // Result is null/undefined/false
            env.services.dialog.closeAll();
            env.services.notification.add(_t("Validation failed - no result returned"), { type: "danger" });
        }
    };

    const handleStockPickingOnClose = async (result, action) => {
        try {
            env.services.dialog.closeAll();
            env.services.notification.add(result.message || "Picking validated successfully", { type: "success" });

            await env.services.action.doAction({
                type: "ir.actions.act_window",
                res_model: "stock.picking",
                res_id: action.params.picking_id,
                views: [[false, "form"]],
                target: "current"
            });
        } catch (error) {
            console.error("Error in wizard onClose:", error);
            env.services.notification.add("Error refreshing form", { type: "danger" });
        }
    };

    try {
        if (action.params.model === "sale.order") {
            const order_line_data = action.params.order_lines.map(line => ({
                id: line.id,
                product_id: line.product_id ? line.product_id[0] : null,
                discount: action.params.discount_percentage
            }));
            const res = await env.services.orm.call("sale.order", "need_employee_selection",
                [action.params.sale_order_id],
                { 'order_line': order_line_data }
            );

            if (!res) {
                await tryToValidateSaleOrder(action);
                env.services.action.doAction({ type: 'ir.actions.act_window_close' });
                return;
            }
        } else if (action.params.model === "stock.picking") {
            console.log("Stock picking validation triggered for picking:", action.params.picking_id);
        }
        const employees = await env.services.orm.call("hr.employee", "get_all_employees", [false]);

        if (!employees.all || employees.all.length === 0) {
            return;
        }

        const filteredEmployees = employees.all
            .filter(employee => employee.is_show === true)
            .map(employee => ({
                ...employee,
                action_approve_sale_order: true
            }));

        if (action.params.model === "sale.order" && filteredEmployees.length === 0) {
            const errorMessage = _t("No Store Manager is assigned. Please check employee settings to ensure a Store Manager is configured.");
            env.services.notification.add(errorMessage, { type: "danger" });
            return false;
        }

        const filteredemployeeList = filteredEmployees.map((employee) => ({
            id: employee.id,
            item: employee,
            label: employee.name,
            barcode: employee.barcode,
            isSelected: false,
        }));
        const allEmployees = employees.all.map((employee) => ({
            id: employee.id,
            item: employee,
            label: employee.name,
            barcode: employee.barcode,
            isSelected: false,
        }));

        const dialogTitle = action.params.model === "sale.order"
            ? _t("Select Employee for Discount Approval")
            : _t("Select Employee for Stock Picking Validation");

        const employeeDialog = env.services.dialog.add(
            DialogWrapper,
            {
                Component: SelectionPopup,
                componentProps: {
                    action: 'validate_with_employee_selection',
                    popupData: {
                        title: dialogTitle,
                        // If sale order, show only filtered employees, otherwise show all employees
                        list: action.params.model === 'sale.order' ? filteredemployeeList : allEmployees,
                    },
                    onClosePopup: async (popupId) => {
                        console.log("Employee selection popup closed");
                        env.services.dialog.closeAll()
                    },
                    onSelectEmployee: async (employeeId, pin) => {
                        try {
                            // Create context based on model type
                            const context = action.params.model === "sale.order" ? {
                                action_approve_sale_order: true,
                                res_model: 'sale.order',
                                res_id: action.params.sale_order_id,
                                action_validate_with_employee_selection: true
                            } : {
                                res_model: 'stock.picking',
                                res_id: action.params.picking_id,
                                action_validate_with_employee_selection: true
                            };

                            const loginResult = await env.services.orm.call(
                                "hr.employee",
                                "login",
                                [employeeId, pin],
                                { context }
                            ).then(async result => {
                                if (result) {
                                    if (action.params.model === "sale.order") {
                                        await tryToValidateSaleOrder(action);
                                    } else if (action.params.model === "stock.picking") {
                                        await tryToValidateStockPicking(action);
                                    }
                                } else {
                                    const employee = filteredEmployees.find(e => e.id === employeeId);
                                    if (employee && !pin) {
                                        // Employee requires PIN - open PIN popup
                                        const pinPopup = env.services.dialog.add(
                                            DialogWrapper,
                                            {
                                                Component: PinPopup,
                                                componentProps: {
                                                    action: 'validate_with_employee_selection',
                                                    popupData: { employee },
                                                    onClosePopup: async (popupId) => {
                                                        console.log("PIN popup closed");
                                                    },
                                                    onPinValidate: async (empId, pinCode) => {
                                                        const pinResult = await validateEmployeeWithPin(empId, pinCode, action);
                                                        if (!pinResult) {
                                                            // PIN validation failed - notification already shown in validateEmployeeWithPin
                                                            console.log("PIN validation failed");
                                                        }
                                                    }
                                                }
                                            },
                                        );
                                    } else if (pin) {
                                        // Employee login failed with PIN provided
                                        env.services.notification.add(_t("Wrong password!"), { type: "danger" });
                                    } else {
                                        // Employee login failed without PIN (shouldn't happen)
                                        env.services.notification.add(_t("Employee login failed!"), { type: "danger" });
                                    }
                                }
                            }).catch(error => {
                                console.error("Error in employee login:", error);
                                env.services.notification.add(_t("Error during employee login: ") + error.message, { type: "danger" });
                            });
                        } catch (error) {
                            console.error("Error in employee selection:", error);
                            env.services.notification.add(_t("Error in employee selection: ") + error.message, { type: "danger" });
                        }
                    }
                },
            }
        );

    } catch (error) {
        console.error("Error in ActionValidateWithEmployeeSelection:", error);
        env.services.notification.add(_t("Error in employee validation workflow: ") + error.message, { type: "danger" });
    }
}

registry.category("actions").add("action_validate_with_employee_selection", ActionValidateWithEmployeeSelection);
