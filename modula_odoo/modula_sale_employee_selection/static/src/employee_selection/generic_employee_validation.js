/** @odoo-module **/

import { _t } from "@web/core/l10n/translation";
import { registry } from "@web/core/registry";
import { DialogWrapper } from "./dialog_wrapper";
import { SelectionPopup } from "./popup";
import { PinPopup } from "./pin_popup";

/**
 * Generic Employee Selection Client Action
 * 
 * This client action provides a reusable employee selection widget that can be
 * embedded into any model's actions. It handles the complete workflow:
 * 1. Load employees based on filter criteria
 * 2. Show employee selection dialog
 * 3. Handle PIN validation
 * 4. Execute target method after employee authentication
 * 5. Handle standardized results (boolean, wizard actions, etc.)
 */
export async function ActionValidateWithEmployeeSelection(env, action) {
    console.log("Generic employee validation triggered", action);
    
    const params = action.params;
    
    // Validate required parameters
    if (!params.model || !params.record_id || !params.method_name) {
        env.services.notification.add(
            _t("Invalid parameters: model, record_id, and method_name are required"), 
            { type: "danger" }
        );
        return;
    }

    try {
        // 1. Optional pre-check to see if employee selection is needed
        if (params.pre_check_method) {
            const needsSelection = await env.services.orm.call(
                params.model,
                params.pre_check_method,
                [params.record_id],
                { context: params.additional_data }
            );
            
            if (!needsSelection) {
                console.log("Pre-check indicates no employee selection needed, executing directly");
                return await executeTargetMethod(env, params);
            }
        }
        
        // 2. Load employees based on filter
        const employees = await loadEmployees(env, params.employee_filter || 'all');
        
        if (!employees.all || employees.all.length === 0) {
            env.services.notification.add(_t("No employees available"), { type: "warning" });
            return;
        }
        
        // 3. Show employee selection dialog
        await showEmployeeSelectionDialog(env, params, employees);
        
    } catch (error) {
        console.error("Error in generic employee validation:", error);
        if (error.message && error.message.includes("Component is destroyed")) {
            console.log("Component destroyed during employee validation, skipping");
            return;
        }
        env.services.notification.add(
            _t("Error in employee validation: ") + error.message, 
            { type: "danger" }
        );
    }
}

/**
 * Load employees based on filter type
 */
const loadEmployees = async (env, filterType) => {
    try {
        const employees = await env.services.orm.call("hr.employee", "get_all_employees", [false]);
        
        // Apply filtering based on filter type
        let filteredEmployees = employees.all || [];
        
        if (filterType === 'filtered') {
            filteredEmployees = filteredEmployees.filter(employee => employee.is_show === true);
        } else if (filterType === 'managers_only') {
            filteredEmployees = filteredEmployees.filter(employee => 
                employee.job_id && employee.job_id[1] && 
                employee.job_id[1].toLowerCase().includes('manager')
            );
        }
        // 'all' filter type shows all employees without filtering
        
        return {
            all: employees.all,
            filtered: filteredEmployees,
            connected: employees.connected || [],
            admin: employees.admin || {}
        };
    } catch (error) {
        console.error("Error loading employees:", error);
        throw error;
    }
};

/**
 * Show employee selection dialog
 */
const showEmployeeSelectionDialog = async (env, params, employees) => {
    // Determine which employee list to show
    const employeeList = params.employee_filter === 'all' ? 
        employees.all : employees.filtered;
    
    // Add action context to employees
    const employeesWithContext = employeeList.map(employee => ({
        ...employee,
        action_approve_sale_order: true  // Keep for backward compatibility
    }));
    
    // Convert to dialog format
    const dialogEmployeeList = employeesWithContext.map((employee) => ({
        id: employee.id,
        item: employee,
        label: employee.name,
        barcode: employee.barcode,
        isSelected: false,
    }));
    
    if (dialogEmployeeList.length === 0) {
        const errorMessage = _t("No employees available for selection. Please check employee configuration.");
        env.services.notification.add(errorMessage, { type: "danger" });
        return;
    }
    
    // Show employee selection dialog
    const employeeDialog = env.services.dialog.add(
        DialogWrapper,
        {
            Component: SelectionPopup,
            componentProps: {
                action: 'validate_with_employee_selection',
                popupData: {
                    title: params.dialog_title || _t("Select Employee"),
                    list: dialogEmployeeList,
                },
                onClosePopup: async (popupId) => {
                    console.log("Employee selection popup closed");
                    env.services.dialog.closeAll();
                },
                onSelectEmployee: async (employeeId, pin) => {
                    await handleEmployeeSelection(env, params, employeeId, pin, employeesWithContext);
                }
            },
        }
    );
};

/**
 * Handle employee selection and PIN validation
 */
const handleEmployeeSelection = async (env, params, employeeId, pin, employees) => {
    try {
        // Create login context
        const loginContext = {
            ...params.login_context,
            res_model: params.model,
            res_id: params.record_id,
        };
        
        // Attempt employee login
        const loginResult = await env.services.orm.call(
            "hr.employee",
            "login",
            [employeeId, pin],
            { context: loginContext }
        );
        
        if (loginResult) {
            // Login successful - execute target method
            await executeTargetMethod(env, params);
        } else {
            // Login failed - handle PIN validation
            const employee = employees.find(e => e.id === employeeId);
            if (employee && !pin) {
                // Employee requires PIN - show PIN popup
                await showPinPopup(env, params, employee, employees);
            } else if (pin) {
                // PIN provided but login failed
                env.services.notification.add(_t("Wrong password!"), { type: "danger" });
            } else {
                // Unexpected login failure
                env.services.notification.add(_t("Employee login failed!"), { type: "danger" });
            }
        }
    } catch (error) {
        console.error("Error in employee selection:", error);
        if (error.message && error.message.includes("Component is destroyed")) {
            console.log("Component destroyed during employee selection, skipping");
            return;
        }
        env.services.notification.add(
            _t("Error in employee selection: ") + error.message, 
            { type: "danger" }
        );
    }
};

/**
 * Show PIN validation popup
 */
const showPinPopup = async (env, params, employee, employees) => {
    const pinPopup = env.services.dialog.add(
        DialogWrapper,
        {
            Component: PinPopup,
            componentProps: {
                action: 'validate_with_employee_selection',
                popupData: { employee },
                onClosePopup: async (popupId) => {
                    console.log("PIN popup closed");
                },
                onPinValidate: async (empId, pinCode) => {
                    const pinResult = await validateEmployeeWithPin(env, params, empId, pinCode);
                    if (pinResult) {
                        await executeTargetMethod(env, params);
                    }
                    // Error notification already handled in validateEmployeeWithPin
                }
            }
        }
    );
};

/**
 * Validate employee with PIN
 */
const validateEmployeeWithPin = async (env, params, employeeId, pinCode) => {
    try {
        const loginContext = {
            ...params.login_context,
            res_model: params.model,
            res_id: params.record_id,
        };
        
        const loginResult = await env.services.orm.call(
            "hr.employee",
            "login",
            [employeeId, pinCode],
            { context: loginContext }
        );
        
        if (!loginResult) {
            env.services.notification.add(_t("Wrong password!"), { type: "danger" });
        }
        
        return loginResult;
    } catch (error) {
        console.error("Error in PIN validation:", error);
        if (error.message && error.message.includes("Component is destroyed")) {
            console.log("Component destroyed during PIN validation, skipping");
            return false;
        }
        env.services.notification.add(
            _t("Error during PIN validation: ") + error.message, 
            { type: "danger" }
        );
        return false;
    }
};

/**
 * Execute the target method after successful employee authentication
 */
const executeTargetMethod = async (env, params) => {
    try {
        env.services.dialog.closeAll();
        
        const result = await env.services.orm.call(
            params.model,
            "action_employee_validation_generic",
            [params.record_id],
            {
                context: {
                    method_name: params.method_name,
                    additional_data: params.additional_data
                }
            }
        );
        
        return await handleStandardizedResult(env, params, result);
        
    } catch (error) {
        console.error("Error executing target method:", error);
        if (error.message && error.message.includes("Component is destroyed")) {
            console.log("Component destroyed during method execution, skipping");
            return;
        }
        env.services.dialog.closeAll();
        env.services.notification.add(
            _t("Error executing action: ") + error.message, 
            { type: "danger" }
        );
    }
};

/**
 * Handle standardized result from target method execution
 */
const handleStandardizedResult = async (env, params, result) => {
    try {
        if (result && result.success) {
            if (result.wizard_action) {
                // Handle wizard action (like backorder wizard)
                console.log("Opening wizard:", result.wizard_action);
                await env.services.action.doAction(result.wizard_action, {
                    onClose: () => refreshRecord(env, params, result.message)
                });
            } else if (result.refresh_form) {
                // Refresh the current record
                await refreshRecord(env, params, result.message);
            } else {
                // Just show success message
                if (result.message) {
                    env.services.notification.add(result.message, { type: "success" });
                }
            }
        } else {
            // Handle failure
            const message = result?.message || "Action failed";
            env.services.notification.add(message, { type: "danger" });
            console.error("Target method failed:", result);
        }
    } catch (error) {
        console.error("Error handling result:", error);
        env.services.notification.add("Error processing result", { type: "danger" });
    }
};

/**
 * Refresh the current record
 */
const refreshRecord = async (env, params, message = null) => {
    try {
        if (message) {
            env.services.notification.add(message, { type: "success" });
        }
        
        // Refresh the form view
        await env.services.action.doAction({
            type: "ir.actions.act_window",
            res_model: params.model,
            res_id: params.record_id,
            views: [[false, "form"]],
            target: "current"
        });
    } catch (error) {
        console.error("Error refreshing record:", error);
        env.services.notification.add("Error refreshing form", { type: "danger" });
    }
};

// Register the generic client action
registry.category("actions").add("action_validate_with_employee_selection", ActionValidateWithEmployeeSelection);
