# Testing Instructions - modula_sale_employee_selection

## 🧪 **Testing Overview**

This document provides comprehensive testing procedures for the `modula_sale_employee_selection` module. The testing covers all major functionality including employee authentication, sales order workflows, stock picking validation, and UI components.

## 🎯 **Prerequisites**

### **1. System Setup**
- Odoo 18.0 instance with the module installed
- `modula_branch` module installed and configured
- At least 2 employees with PIN codes configured
- At least 2 branches configured
- User with system administrator privileges
- User with branch-specific access

### **2. Test Data Setup**
```python
# Create test employees
employee1 = env['hr.employee'].create({
    'name': 'Test Employee 1',
    'pin': '1234',
    'employee_type': 'employee',
})

employee2 = env['hr.employee'].create({
    'name': 'Test Employee 2',
    'pin': '5678',
    'employee_type': 'employee',
})

# Create test branches
branch1 = env['res.branch'].create({
    'name': 'Test Branch 1',
    'employee_ids': [(6, 0, [employee1.id])],
})

branch2 = env['res.branch'].create({
    'name': 'Test Branch 2',
    'employee_ids': [(6, 0, [employee2.id])],
})
```

## 🔐 **Employee Authentication Testing**

### **Test Case 1: Basic PIN Authentication**

**Objective**: Verify that employees can authenticate using their PIN codes.

**Steps**:
1. Navigate to Sales → Orders → Quotations
2. Click "Create" to create a new quotation
3. Employee selection popup should appear
4. Select an employee from the list
5. Enter the employee's PIN code
6. Verify that the employee is successfully logged in
7. Verify that the quotation is created with the employee assigned

**Expected Results**:
- Employee selection popup appears automatically
- PIN validation works correctly
- Employee is assigned to the quotation
- Success notification is displayed

**Test Data**:
- Employee: Test Employee 1
- PIN: 1234

### **Test Case 2: Invalid PIN Authentication**

**Objective**: Verify that invalid PIN codes are properly rejected.

**Steps**:
1. Navigate to Sales → Orders → Quotations
2. Click "Create" to create a new quotation
3. Select an employee from the list
4. Enter an incorrect PIN code
5. Verify that the PIN is rejected
6. Verify that an error message is displayed

**Expected Results**:
- PIN validation fails with incorrect PIN
- Error message "Wrong password!" is displayed
- Employee is not logged in
- Quotation is not created

**Test Data**:
- Employee: Test Employee 1
- Incorrect PIN: 9999

### **Test Case 3: Employee Logout**

**Objective**: Verify that employees can logout properly.

**Steps**:
1. Login as an employee (follow Test Case 1)
2. Click on the employee selection button again
3. Select the same employee
4. Verify that the employee is logged out
5. Verify that the session is cleared

**Expected Results**:
- Employee is successfully logged out
- Session is cleared
- UI updates to reflect logged out state

## 🛒 **Sales Order Testing**

### **Test Case 4: Sales Order Creation with Employee**

**Objective**: Verify that sales orders are created with proper employee assignment.

**Steps**:
1. Login as an employee (follow Test Case 1)
2. Create a new sales quotation
3. Add products to the quotation
4. Save the quotation
5. Verify that the employee is assigned to the quotation

**Expected Results**:
- Employee is automatically assigned to the quotation
- Employee field shows the logged-in employee
- Session is cleared after assignment

### **Test Case 5: Sales Order Approval Workflow**

**Objective**: Verify the approval workflow for sales orders requiring approval.

**Prerequisites**: Configure a product or discount that requires approval.

**Steps**:
1. Create a sales quotation
2. Add a product that requires approval (or apply a discount)
3. Verify that the "Approve" button appears in the status bar
4. Click the "Approve" button
5. Employee selection popup should appear
6. Select a manager employee
7. Enter the manager's PIN
8. Verify that the approval is processed

**Expected Results**:
- "Approve" button appears when approval is required
- Employee selection popup opens for approval
- Manager can authenticate and approve
- Order is saved with approval

### **Test Case 6: Sales Order Line Approval**

**Objective**: Verify that individual order lines can require approval.

**Steps**:
1. Create a sales quotation
2. Add multiple products
3. Apply a discount to one line that requires approval
4. Verify that the approval button appears
5. Follow the approval workflow

**Expected Results**:
- Approval button appears for specific lines
- Backend correctly identifies which lines need approval
- Approval workflow works for line-level approvals

## 📦 **Stock Picking Testing**

### **Test Case 7: Stock Picking Validation with Employee**

**Objective**: Verify that stock pickings require employee validation.

**Steps**:
1. Create a delivery order
2. Add products to the delivery
3. Click "Validate" button
4. Verify that employee selection popup appears
5. Select an employee and enter PIN
6. Verify that the picking is validated with employee assignment

**Expected Results**:
- Employee selection is required before validation
- Employee is assigned to the picking
- Picking is successfully validated
- Employee field shows the assigned employee

### **Test Case 8: Stock Picking Validation Without Employee**

**Objective**: Verify that validation fails without employee selection.

**Steps**:
1. Create a delivery order
2. Add products to the delivery
3. Try to validate without selecting an employee
4. Verify that validation fails with error message

**Expected Results**:
- Validation fails with error message
- Error message: "Please select an employee before validating the delivery order."
- Picking is not validated

### **Test Case 9: Backorder Wizard Integration**

**Objective**: Verify that backorder wizards work with employee assignment.

**Steps**:
1. Create a delivery order with partial quantities
2. Validate the picking with employee selection
3. If backorder wizard appears, verify it works correctly
4. Verify that backorder is created without employee assignment

**Expected Results**:
- Backorder wizard appears when needed
- Backorder is created without employee assignment
- Original picking retains employee assignment

## 🌿 **Branch-Based Filtering Testing**

### **Test Case 10: Branch-Based Employee Filtering**

**Objective**: Verify that employees are filtered based on user's branch access.

**Steps**:
1. Login as a user with branch-specific access
2. Navigate to employee selection
3. Verify that only employees from user's branches are shown
4. Login as a system administrator
5. Verify that all employees are shown

**Expected Results**:
- Branch users see only their branch employees
- System administrators see all employees
- Filtering works correctly for different user types

### **Test Case 11: Users Without Branch Assignment**

**Objective**: Verify that users without branch assignment see all employees.

**Steps**:
1. Create a user without branch assignment
2. Login as this user
3. Navigate to employee selection
4. Verify that all employees are shown

**Expected Results**:
- Users without branches see all employees
- No filtering is applied
- Employee selection works normally

## 🎨 **UI Component Testing**

### **Test Case 12: Employee Selection Popup**

**Objective**: Verify that the employee selection popup works correctly.

**Steps**:
1. Trigger employee selection (create quotation or validate picking)
2. Verify popup appears with employee list
3. Test search functionality
4. Test employee selection
5. Test popup cancellation

**Expected Results**:
- Popup displays employee list correctly
- Search functionality works
- Employee selection works
- Popup can be cancelled

### **Test Case 13: PIN Popup**

**Objective**: Verify that the PIN popup works correctly.

**Steps**:
1. Select an employee from the selection popup
2. Verify PIN popup appears
3. Test numeric keypad input
4. Test keyboard input (0-9, Delete, Backspace, Enter, Escape)
5. Test invalid PIN entry
6. Test valid PIN entry

**Expected Results**:
- PIN popup appears after employee selection
- Numeric keypad works correctly
- Keyboard input works correctly
- Invalid PIN shows error message
- Valid PIN proceeds to next step

### **Test Case 14: Non-Closable Dialog**

**Objective**: Verify that dialogs cannot be accidentally closed during critical operations.

**Steps**:
1. Open employee selection popup
2. Try to close the popup using ESC key
3. Try to close the popup by clicking outside
4. Verify that popup remains open
5. Only allow closure through proper actions

**Expected Results**:
- Popup cannot be closed accidentally
- Only proper actions (selection, cancellation) close the popup
- User must complete the workflow

### **Test Case 15: Reactive Button Visibility**

**Objective**: Verify that approval buttons appear/disappear based on form state.

**Steps**:
1. Create a sales quotation
2. Add products that don't require approval
3. Verify no approval button appears
4. Add a product or discount that requires approval
5. Verify approval button appears
6. Remove the approval requirement
7. Verify approval button disappears

**Expected Results**:
- Button visibility updates in real-time
- Backend integration works correctly
- UI reflects current approval requirements

## 🔧 **Integration Testing**

### **Test Case 16: Client Action Integration**

**Objective**: Verify that client actions work correctly for cross-module integration.

**Steps**:
1. Create a custom module that uses the client action
2. Trigger the client action
3. Verify employee selection workflow
4. Verify proper integration with the module

**Expected Results**:
- Client action triggers employee selection
- Workflow completes successfully
- Integration works seamlessly

### **Test Case 17: Template Method Extensibility**

**Objective**: Verify that template methods can be extended by dependent modules.

**Steps**:
1. Create a test module that extends the template methods
2. Override `need_employee_selection()` method
3. Override `action_employee_validation_for_sale_order()` method
4. Test the extended functionality

**Expected Results**:
- Template methods can be overridden
- Extended functionality works correctly
- Base functionality remains intact

## 🚨 **Error Handling Testing**

### **Test Case 18: Network Error Handling**

**Objective**: Verify that network errors are handled gracefully.

**Steps**:
1. Simulate network disconnection
2. Try to perform employee selection
3. Verify error handling and user feedback

**Expected Results**:
- Errors are caught and handled
- User receives appropriate error messages
- System remains stable

### **Test Case 19: Session Error Handling**

**Objective**: Verify that session errors are handled gracefully.

**Steps**:
1. Simulate session timeout
2. Try to perform employee operations
3. Verify error handling and recovery

**Expected Results**:
- Session errors are handled gracefully
- User is prompted to re-authenticate if needed
- System recovers from session issues

## 📊 **Performance Testing**

### **Test Case 20: Large Employee Dataset**

**Objective**: Verify performance with large numbers of employees.

**Steps**:
1. Create 100+ test employees
2. Test employee selection popup performance
3. Test filtering performance
4. Verify UI responsiveness

**Expected Results**:
- Popup loads within acceptable time
- Filtering works efficiently
- UI remains responsive

### **Test Case 21: Concurrent User Testing**

**Objective**: Verify system behavior with multiple concurrent users.

**Steps**:
1. Have multiple users perform employee selection simultaneously
2. Test session management
3. Verify no conflicts or data corruption

**Expected Results**:
- Multiple users can work simultaneously
- Sessions are properly isolated
- No data conflicts occur

## 🔒 **Security Testing**

### **Test Case 22: PIN Security**

**Objective**: Verify that PIN security measures work correctly.

**Steps**:
1. Test PIN validation with various inputs
2. Test session security
3. Verify that PINs are not exposed in logs or UI

**Expected Results**:
- PIN validation is secure
- PINs are not exposed
- Session security is maintained

### **Test Case 23: Access Control**

**Objective**: Verify that access control works correctly.

**Steps**:
1. Test with different user roles
2. Test branch-based access control
3. Verify that unauthorized access is prevented

**Expected Results**:
- Access control works correctly
- Unauthorized access is prevented
- Branch filtering works as expected

## 📝 **Test Reporting**

### **Test Results Template**

For each test case, record the following:

```markdown
## Test Case [Number]: [Title]

**Date**: [Date]
**Tester**: [Name]
**Environment**: [Odoo version, module version]

**Steps Executed**:
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Results**:
- [Expected result 1]
- [Expected result 2]

**Actual Results**:
- [Actual result 1]
- [Actual result 2]

**Status**: ✅ PASS / ❌ FAIL / ⚠️ PARTIAL

**Notes**:
[Additional notes, issues found, recommendations]
```

### **Bug Reporting Template**

```markdown
## Bug Report

**Title**: [Brief description of the bug]

**Severity**: Critical / High / Medium / Low

**Steps to Reproduce**:
1. [Step 1]
2. [Step 2]
3. [Step 3]

**Expected Behavior**:
[What should happen]

**Actual Behavior**:
[What actually happens]

**Environment**:
- Odoo Version: [Version]
- Module Version: [Version]
- Browser: [Browser and version]
- User Role: [Role]

**Screenshots**:
[If applicable]

**Additional Information**:
[Any additional context or information]
```

## 🎯 **Testing Checklist**

### **Pre-Testing Checklist**
- [ ] Test environment is properly configured
- [ ] Test data is created and verified
- [ ] All dependencies are installed
- [ ] Test users are created with appropriate permissions
- [ ] Test employees are created with PIN codes
- [ ] Test branches are configured

### **Functional Testing Checklist**
- [ ] Employee authentication works correctly
- [ ] Sales order workflows function properly
- [ ] Stock picking validation works correctly
- [ ] Branch-based filtering works as expected
- [ ] UI components function correctly
- [ ] Error handling works properly

### **Security Testing Checklist**
- [ ] PIN validation is secure
- [ ] Access control works correctly
- [ ] Session management is secure
- [ ] No sensitive data is exposed

### **Performance Testing Checklist**
- [ ] System performs well with large datasets
- [ ] Concurrent users can work simultaneously
- [ ] UI remains responsive
- [ ] No memory leaks or performance degradation

### **Integration Testing Checklist**
- [ ] Client actions work correctly
- [ ] Template methods can be extended
- [ ] Cross-module integration works
- [ ] API stability is maintained

## 🔄 **Continuous Testing**

### **Automated Testing Recommendations**

1. **Unit Tests**: Create unit tests for core functionality
2. **Integration Tests**: Test module integration with core Odoo
3. **UI Tests**: Automated UI testing for critical workflows
4. **Performance Tests**: Automated performance testing
5. **Security Tests**: Automated security testing

### **Testing Schedule**

- **Daily**: Run smoke tests on critical functionality
- **Weekly**: Run full test suite
- **Monthly**: Performance and security testing
- **Before Release**: Complete regression testing

This comprehensive testing guide ensures that all aspects of the `modula_sale_employee_selection` module are thoroughly tested and validated before deployment.
