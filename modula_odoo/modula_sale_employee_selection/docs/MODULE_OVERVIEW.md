# Module Overview - modula_sale_employee_selection

## 🎯 **Module Purpose**

The `modula_sale_employee_selection` module provides a comprehensive employee selection and validation system for Odoo 18, enabling secure employee authentication and approval workflows across sales and inventory operations. This module extends the core Odoo functionality to require employee identification before critical business operations like sales order approvals and stock picking validations.

## 🏗️ **Core Architecture**

### **Backend Architecture**

#### **1. Employee Management (`hr_employee.py`)**
- **Session Management**: Handles multiple session types (`EMPLOYEES_CONNECTED`, `SESSION_OWNER`, `MANAGER_APPROVE`)
- **PIN Authentication**: Secure PIN validation with context-aware login/logout
- **Branch Filtering**: Employee access control based on user branch permissions from `modula_branch`
- **Stock Picking Validation**: Integrated validation with backorder wizard support
- **Template Methods**: Extensible `_get_employee_is_show()` for dependent modules
- **Manager Approval**: Special session handling for manager approval workflows

#### **2. Sales Order Integration (`sale_order.py`)**
- **Employee Assignment**: Automatic employee assignment on order creation from session
- **Template Methods**: `need_employee_selection()` and `action_employee_validation_for_sale_order()` for extensibility
- **Session Cleanup**: Proper session management during order operations
- **Approval Workflows**: Support for discount approval requiring manager validation

#### **3. Sales Order Line (`sale_order_line.py`)**
- **Template Methods**: `need_employee_selection()` for line-level approval logic
- **Extensibility**: Base implementation for dependent modules to override

#### **4. Stock Picking (`stock_picking.py`)**
- **Employee Validation**: Required employee selection before picking validation
- **Backorder Support**: Context-aware backorder creation without employee assignment
- **Error Handling**: Validation errors for missing employee selection
- **Copy Protection**: Employee field clearing during backorder creation
- **Client Action**: `action_validate_with_employee_selection` for frontend integration

### **Frontend Architecture**

#### **1. Employee Hooks (`employee_hooks.js`)**
- **Core Logic**: Centralized employee selection and session management
- **Client Action**: `ActionValidateWithEmployeeSelection` for discount approval workflows
- **Dialog Management**: Consistent popup handling with `DialogWrapper`
- **Service Integration**: ORM calls, notifications, and action management
- **Branch Filtering**: Frontend support for employee filtering logic
- **Form Integration**: Callback management for form save and refresh operations

#### **2. Form Controller (`controller.js`)**
- **Sales Integration**: Form controller override for employee selection
- **Employee Initialization**: Automatic employee setup on form load
- **Callback Management**: Form save and refresh callbacks
- **Context Handling**: Employee context management during form operations
- **Action Integration**: Integration with sale order and quotation actions

#### **3. Status Bar Buttons (`employee_selection_button.js`)**
- **Reactive UI**: Dynamic button visibility based on form state
- **Model Integration**: Real-time updates from backend state
- **Multi-Model Support**: Works with both sale orders and stock pickings
- **Validation Logic**: Backend integration for approval requirements
- **State Management**: Reactive state updates for approval workflows

#### **4. View Registration (`views.js`)**
- **Form Override**: Global form view override for employee selection
- **Registry Management**: Proper view registration and cleanup

### **UI Components**

#### **1. Popup Components**
- **Selection Popup**: Employee selection interface with search and filtering
- **PIN Popup**: Secure PIN validation with numpad interface and keyboard support
- **Dialog Wrapper**: Consistent dialog management across components
- **Non-Closable Dialog**: Prevents accidental dialog closure during critical operations

#### **2. Button Templates**
- **Status Bar Integration**: Seamless integration with form status bars
- **Dynamic Visibility**: Context-aware button display
- **Hotkey Support**: Keyboard shortcuts for quick access

#### **3. View Customization**
- **Stock Picking Views**: Hide original validate buttons
- **Form Inheritance**: Template-based view customization

## 🔄 **Workflow Patterns**

### **1. Employee Authentication Workflow**
```
1. User clicks employee selection button
2. System loads available employees (filtered by branch)
3. User selects employee from popup
4. System prompts for PIN if required
5. Employee logs in with session management
6. UI updates to reflect logged-in employee
7. Original action proceeds with employee context
```

### **2. Sales Order Approval Workflow**
```
1. User modifies discount on sale order line
2. System checks if approval is required (template method)
3. If approval needed, "Approve" button appears
4. User clicks "Approve" button
5. Employee selection popup opens
6. Manager selects employee and enters PIN
7. System validates employee and applies discount
8. Order is saved with employee assignment
```

### **3. Stock Picking Validation Workflow**
```
1. User clicks "Validate" button on stock picking
2. System checks if employee is selected
3. If no employee, employee selection popup opens
4. User selects employee and enters PIN
5. System validates employee and assigns to picking
6. Picking validation proceeds with backorder support
7. If backorder wizard needed, it opens with employee context
```

### **4. Cross-Module Integration Workflow**
```
1. External module triggers client action
2. Client action checks if employee selection needed
3. Employee selection popup opens if required
4. Employee validation proceeds with PIN check
5. Original action completes with employee context
6. UI updates to reflect completed action
```

## 🔧 **Technical Patterns**

### **1. Template Method Pattern**
```python
# Base implementation
def need_employee_selection(self, **kwargs):
    return False

def action_employee_validation_for_sale_order(self):
    self.ensure_one()
    return True

def _get_employee_is_show(self, employee_data):
    return True
```

### **2. Session Management Pattern**
```python
# Session constants
EMPLOYEES_CONNECTED = "employees_connected"
SESSION_OWNER = "session_owner"
MANAGER_APPROVE = "manager_approve"

# Context-aware login
def login(self, pin=False, set_in_session=True):
    if self.pin_validation(pin) and set_in_session:
        if self.env.context.get('action_approve_sale_order'):
            request.session[MANAGER_APPROVE] = self.id
        else:
            self._connect_employee()
            request.session[SESSION_OWNER] = self.id
        return True
    return False
```

### **3. Reactive UI Pattern**
```javascript
// Reactive state
this.needApprove = useState({ value: false });

// Model change listening
useBus(this.env.model.bus, "update", async () => {
    await this.updateNeedApproveFromBackend();
});

// Dynamic button visibility
get shouldShowApproveButton() {
    const record = this.env.model.root;
    const modelNeedApprove = record && record.data && record.data.need_approve;
    const stateNeedApprove = this.needApprove?.value;
    return this.env.model?.root ? (modelNeedApprove || stateNeedApprove) : false;
}
```

### **4. Client Action Pattern**
```javascript
export async function ActionValidateWithEmployeeSelection(env, action) {
    try {
        // Check if employee selection needed
        const res = await env.services.orm.call("sale.order", "need_employee_selection", [params]);
        
        if (!res) {
            // Proceed without employee selection
            await env.services.orm.call("sale.order", "action_employee_validation_for_sale_order", [params]);
            env.services.action.doAction({ type: 'ir.actions.act_window_close' });
            return;
        }
        
        // Show employee selection popup
        const employeeDialog = env.services.dialog.add(DialogWrapper, {
            Component: SelectionPopup,
            componentProps: { /* ... */ }
        });
    } catch (error) {
        console.error("Error in ActionValidateWithEmployeeSelection:", error);
    }
}
```

### **5. Dialog Management Pattern**
```javascript
const openDialog = (id, component, props) => {
    popup[id] = {
        isShown: true,
        close: dialog.add(
            DialogWrapper,
            {
                Component: component,
                componentProps: { ...props, action: props.action },
            },
            {
                onClose: () => {
                    popup[id] = { isShown: false };
                },
            }
        ),
    };
};
```

## 🔗 **Integration Points**

### **1. Core Odoo Modules**
- **`web`**: Frontend framework and UI components
- **`hr`**: Employee management and authentication
- **`sale_management`**: Sales order processing and workflows
- **`stock`**: Inventory management and picking operations

### **2. Custom Modules**
- **`modula_branch`**: Branch-based employee filtering and access control

### **3. External Dependencies**
- **Odoo 18 Framework**: Modern OWL components and reactive patterns
- **Session Management**: Secure employee session handling
- **PIN Authentication**: Employee identification and validation

## 🎯 **Target Users**

### **Primary Users**
- **Sales Representatives**: Require employee identification for order processing
- **Warehouse Staff**: Need employee validation for stock operations
- **Managers**: Approve discounts and special pricing with employee tracking
- **System Administrators**: Configure employee access and branch permissions

### **Secondary Users**
- **Accountants**: Track employee-based transactions
- **Auditors**: Review employee activity and approvals
- **HR Staff**: Manage employee PINs and access rights

## 🚀 **Key Features**

### **1. Employee Authentication**
- Secure PIN-based employee identification
- Session management for multiple employees
- Branch-based access control
- Manager approval workflows

### **2. Sales Integration**
- Automatic employee assignment on order creation
- Discount approval workflows
- Employee tracking for all sales activities
- Template methods for extensibility

### **3. Inventory Integration**
- Employee validation before stock picking
- Backorder wizard integration
- Employee assignment to picking operations
- Copy protection for backorder creation

### **4. UI/UX Features**
- Reactive button visibility
- Non-closable dialogs for critical operations
- Keyboard shortcuts for PIN entry
- Real-time form updates
- Consistent dialog management

## 🔒 **Security Considerations**

### **1. Session Security**
- Secure session management with multiple session types
- PIN validation with proper error handling
- Session cleanup on logout and form operations
- Branch-based access control

### **2. Data Protection**
- Employee data filtering based on user permissions
- Secure PIN storage and validation
- Audit trail for employee actions
- Validation errors for unauthorized access

### **3. UI Security**
- Non-closable dialogs prevent bypass
- Proper error handling and user feedback
- Session state validation
- Form save callbacks for data integrity

## 📈 **Performance Considerations**

### **1. Backend Performance**
- Efficient employee filtering by branch
- Optimized session management
- Minimal database queries for employee data
- Template methods for extensibility

### **2. Frontend Performance**
- Reactive state management
- Efficient dialog handling
- Minimal re-renders with proper state updates
- Lazy loading of employee data

### **3. Scalability**
- Branch-based filtering for large employee sets
- Session management for multiple concurrent users
- Template methods for easy extension
- Modular architecture for maintainability 
// Nested dialog handling
const pinPopup = env.services.dialog.add(DialogWrapper, {
    Component: PinPopup,
    componentProps: {
        onClosePopup: async (popupId) => {
            env.services.dialog.closeAll();
        }
    }
});
```

## 🔐 **Security & Access Control**

### **1. Branch-based Employee Filtering**
- Employees filtered based on user's branch access
- System administrators see all employees
- Branch-specific employee access control
- Permission-based employee visibility

### **2. PIN Authentication**
- Secure PIN validation for employee login
- Session-based authentication
- Context-aware authentication (approval vs. regular)
- Automatic session cleanup

### **3. Access Control**
- Model-level access control via `ir.model.access.csv`
- User group-based permissions
- Branch-based access restrictions
- Employee type filtering

## 📊 **Data Flow**

### **1. Employee Data Flow**
```
Database → Branch Filtering → PIN Validation → Session Management → UI Display
```

### **2. Approval Data Flow**
```
Form Changes → Backend Check → UI Update → Employee Selection → PIN Validation → Approval → Save
```

### **3. Validation Data Flow**
```
Validate Button → Employee Check → Employee Selection → PIN Validation → Assignment → Validation → Backorder Handling
```

## 🔄 **Integration Points**

### **1. Sales Module Integration**
- Sale order creation with employee assignment
- Order line discount approval workflows
- Employee context in sales operations
- Template method extensibility

### **2. Stock Module Integration**
- Picking validation with employee requirement
- Backorder wizard integration
- Employee assignment tracking
- Validation error handling

### **3. HR Module Integration**
- Employee management and authentication
- Branch-based employee filtering
- PIN-based security
- Session management

### **4. Web Module Integration**
- Form view customization
- Status bar button integration
- Dialog management
- Client action implementation

## 🎯 **Extensibility Points**

### **1. Template Methods**
- `need_employee_selection()` - Determine if approval needed
- `action_employee_validation_for_sale_order()` - Custom approval logic
- `_get_employee_is_show()` - Employee visibility logic

### **2. Client Actions**
- `action_validate_with_employee_selection` - Cross-module integration
- Custom client actions for specific workflows

### **3. UI Components**
- Custom popup components
- Extended button functionality
- Additional form integrations

### **4. Session Management**
- Custom session types
- Extended authentication logic
- Branch-specific workflows

## 🚀 **Performance Considerations**

### **1. Employee Loading**
- Efficient employee filtering by branch
- Cached employee data in session
- Minimal database queries

### **2. UI Responsiveness**
- Reactive state management
- Efficient form updates
- Optimized dialog handling

### **3. Session Management**
- Minimal session data
- Efficient session cleanup
- Context-aware session handling

## 🔧 **Configuration**

### **1. Module Dependencies**
- `web` - Core web framework
- `hr` - Employee management
- `sale_management` - Sales functionality
- `stock` - Inventory management

### **2. Asset Registration**
```python
'assets': {
    'web.assets_backend': [
        'modula_sale_employee_selection/static/**/*',
    ],
}
```

### **3. View Inheritance**
- Form view override for employee selection
- Status bar button integration
- Stock picking view customization

## 📈 **Scalability**

### **1. Multi-Branch Support**
- Branch-based employee filtering
- Scalable employee management
- Permission-based access control

### **2. Template Method Extensibility**
- Easy addition of new approval workflows
- Customizable employee selection logic
- Modular architecture for dependent modules

### **3. Session Management**
- Efficient session handling for multiple users
- Context-aware session management
- Scalable authentication system

## Recent Updates (Latest Changes)

### Enhanced Branch-Based Employee Filtering (Latest Update)
**File**: `models/hr_employee.py` - `get_all_employees` method

**Key Enhancements**:
- **Enhanced Branch Logic**: Improved branch filtering to handle users without assigned branches
- **New Requirement Implementation**: Users without assigned branches can now see all employees without filtering
- **Improved Code Structure**: Cleaner, more maintainable branch filtering logic
- **Enhanced Error Handling**: Better handling of edge cases in branch assignment

**Business Logic**:
1. **System Administrators**: Always see all employees
2. **Users with Branch Assignment**: See only employees from their assigned branches
3. **Users without Branch Assignment**: See all employees without filtering (NEW REQUIREMENT)
4. **Context Override**: `allowed_branch_ids` in context can override user's branch assignment

This module provides a robust, scalable, and extensible foundation for employee selection and validation workflows in Odoo 18, with comprehensive integration across sales and inventory operations. 