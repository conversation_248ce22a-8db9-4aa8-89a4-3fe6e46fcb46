# modula_sale_employee_selection

## 📋 **Module Overview**

The `modula_sale_employee_selection` module provides comprehensive employee selection and validation functionality for Odoo 18. It requires employee identification before critical business operations like sales order approvals and stock picking validations, ensuring proper accountability and workflow management.

## 🚀 **Quick Start**

### **Installation**

1. **Install Dependencies**:
   ```bash
   # Ensure modula_branch module is installed
   # This module provides branch-based employee filtering
   ```

2. **Install Module**:
   ```bash
   # Install the module through Odoo Apps
   # Or place in addons directory and update module list
   ```

3. **Configure Employees**:
   - Navigate to **Employees** → **Employees**
   - Create or edit employees
   - Set PIN codes for authentication
   - Assign employees to branches (if using branch filtering)

4. **Configure User Branches**:
   - Navigate to **Settings** → **Users & Companies** → **Users**
   - Assign users to branches for access control
   - System administrators see all employees

### **Basic Usage**

#### **Sales Order Creation**
1. Navigate to **Sales** → **Orders** → **Quotations**
2. Click **Create**
3. Employee selection popup appears automatically
4. Select an employee and enter PIN
5. Quotation is created with employee assignment

#### **Sales Order Approval**
1. Create or edit a sales order
2. Apply discount or special pricing (if approval required)
3. **Approve** button appears in status bar
4. Click **Approve** and select manager employee
5. Enter manager's PIN for approval
6. Order is saved with approval

#### **Stock Picking Validation**
1. Navigate to **Inventory** → **Operations** → **Transfers**
2. Open a delivery order ready for validation
3. Click **Validate** button
4. Employee selection popup appears
5. Select employee and enter PIN
6. Picking is validated with employee assignment

## 🏗️ **Architecture**

### **Core Components**

#### **Backend Models**
- **`hr_employee.py`**: Employee management, session handling, PIN validation
- **`sale_order.py`**: Sales order integration and employee assignment
- **`sale_order_line.py`**: Sales order line approval workflows
- **`stock_picking.py`**: Stock picking validation with employee requirement

#### **Frontend Components**
- **`employee_hooks.js`**: Core employee selection logic and session management
- **`controller.js`**: Form controller integration
- **`employee_selection_button.js`**: Reactive status bar buttons
- **`popup.js`**: Employee selection popup interface
- **`pin_popup.js`**: PIN validation popup

### **Key Features**

#### **Employee Authentication**
- Secure PIN-based employee identification
- Session management for multiple employees
- Context-aware authentication (approval vs. regular operations)
- Branch-based access control

#### **Sales Integration**
- Automatic employee assignment on order creation
- Discount approval workflows with manager validation
- Employee tracking for all sales activities
- Template methods for extensibility

#### **Inventory Integration**
- Employee validation before stock picking
- Backorder wizard integration
- Employee assignment to picking operations
- Copy protection for backorder creation

#### **UI/UX Features**
- Reactive button visibility based on form state
- Non-closable dialogs for critical operations
- Keyboard shortcuts for PIN entry
- Real-time form updates
- Consistent dialog management

## 🔧 **Configuration**

### **Module Dependencies**
```python
'depends': [
    'web',           # Frontend framework and UI components
    'hr',            # Employee management and authentication
    'sale_management', # Sales order processing and workflows
    'stock',         # Inventory management and picking operations
    'modula_branch', # Branch-based employee filtering and access control
]
```

### **Employee Setup**
1. **Create Employees**:
   - Navigate to **Employees** → **Employees**
   - Create employees with names and PIN codes
   - Set employee type to "Employee"

2. **Configure PIN Codes**:
   - PIN codes are used for employee authentication
   - PINs should be secure and not easily guessable
   - Consider implementing PIN complexity requirements

3. **Branch Assignment**:
   - Assign employees to branches for access control
   - Users see only employees from their assigned branches
   - System administrators see all employees

### **User Configuration**
1. **Branch Assignment**:
   - Assign users to branches for employee filtering
   - Users without branch assignment see all employees
   - Context can override user's branch assignment

2. **Permissions**:
   - Ensure users have appropriate permissions
   - System administrators have full access
   - Regular users have branch-based access

## 🔄 **Workflows**

### **Employee Authentication Workflow**
```
1. User triggers employee selection (create order, validate picking, etc.)
2. System loads available employees (filtered by branch)
3. User selects employee from popup
4. System prompts for PIN if required
5. Employee logs in with session management
6. UI updates to reflect logged-in employee
7. Original action proceeds with employee context
```

### **Sales Order Approval Workflow**
```
1. User modifies discount on sale order line
2. System checks if approval is required (template method)
3. If approval needed, "Approve" button appears
4. User clicks "Approve" button
5. Employee selection popup opens
6. Manager selects employee and enters PIN
7. System validates employee and applies discount
8. Order is saved with employee assignment
```

### **Stock Picking Validation Workflow**
```
1. User clicks "Validate" button on stock picking
2. System checks if employee is selected
3. If no employee, employee selection popup opens
4. User selects employee and enters PIN
5. System validates employee and assigns to picking
6. Picking validation proceeds with backorder support
7. If backorder wizard needed, it opens with employee context
```

## 🔒 **Security**

### **PIN Authentication**
- Secure PIN validation with sudo access
- Session-based authentication with proper cleanup
- Context-aware authentication for different workflows
- PINs are never logged or exposed

### **Access Control**
- Branch-based employee filtering
- System administrator override for full access
- User permission-based access control
- Session security for employee state

### **UI Security**
- Non-closable dialogs prevent accidental closure
- Proper error handling and user feedback
- Session state validation
- Form save callbacks for data integrity

## 📊 **Performance**

### **Backend Performance**
- Efficient branch-based filtering for large employee sets
- Optimized session management for multiple users
- Minimal database queries for employee data
- Template methods for extensibility

### **Frontend Performance**
- Reactive state management with minimal re-renders
- Lazy loading of employee data
- Efficient dialog handling and cleanup
- Optimized form updates and callbacks

### **Scalability**
- Branch-based architecture scales with multiple branches
- Session management handles multiple concurrent users
- Template methods allow easy extension
- Modular design for maintainability

## 🔧 **Development**

### **Template Methods**
The module provides template methods for extensibility:

```python
# In sale_order.py
def need_employee_selection(self, **kwargs):
    """Template method: Determine if approval is required"""
    return False

def action_employee_validation_for_sale_order(self):
    """Template method: Custom approval logic"""
    self.ensure_one()
    return True

# In hr_employee.py
def _get_employee_is_show(self, employee_data):
    """Template method: Employee visibility logic"""
    return True
```

### **Client Actions**
Cross-module integration using client actions:

```javascript
export async function ActionValidateWithEmployeeSelection(env, action) {
    const params = action.params;
    
    try {
        if (params.model === 'sale.order') {
            await tryToValidateSaleOrder(action);
        } else if (params.model === 'stock.picking') {
            await tryToValidateStockPicking(action);
        }
    } catch (error) {
        console.error("Error in ActionValidateWithEmployeeSelection:", error);
        env.services.notification.add("Error in employee validation", { type: "danger" });
    }
}
```

### **Session Management**
Robust session management with multiple session types:

```python
# Session constants
EMPLOYEES_CONNECTED = "employees_connected"  # List of connected employees
SESSION_OWNER = "session_owner"              # Primary employee session
MANAGER_APPROVE = "manager_approve"          # Manager approval session

# Context-aware login
def login(self, pin=False, set_in_session=True):
    if self.pin_validation(pin) and set_in_session:
        if self.env.context.get('action_approve_sale_order'):
            request.session[MANAGER_APPROVE] = self.id
        else:
            self._connect_employee()
            request.session[SESSION_OWNER] = self.id
        return True
    return False
```

## 🧪 **Testing**

### **Test Scenarios**
1. **Employee Authentication**: Test PIN validation and session management
2. **Sales Order Workflows**: Test order creation and approval processes
3. **Stock Picking Validation**: Test picking validation with employee requirement
4. **Branch Filtering**: Test employee filtering based on user branches
5. **UI Components**: Test popup interfaces and button visibility
6. **Error Handling**: Test error scenarios and user feedback
7. **Performance**: Test with large employee datasets
8. **Security**: Test access control and PIN security

### **Test Data Setup**
```python
# Create test employees
employee1 = env['hr.employee'].create({
    'name': 'Test Employee 1',
    'pin': '1234',
    'employee_type': 'employee',
})

employee2 = env['hr.employee'].create({
    'name': 'Test Employee 2',
    'pin': '5678',
    'employee_type': 'employee',
})

# Create test branches
branch1 = env['res.branch'].create({
    'name': 'Test Branch 1',
    'employee_ids': [(6, 0, [employee1.id])],
})

branch2 = env['res.branch'].create({
    'name': 'Test Branch 2',
    'employee_ids': [(6, 0, [employee2.id])],
})
```

## 🚨 **Troubleshooting**

### **Common Issues**

#### **Employee Selection Popup Not Appearing**
- Check if employee selection is triggered correctly
- Verify that employees are available for the user's branch
- Check browser console for JavaScript errors
- Ensure module is properly installed and configured

#### **PIN Validation Failing**
- Verify PIN codes are set correctly for employees
- Check if user has permission to access employee data
- Ensure session management is working properly
- Check for network connectivity issues

#### **Branch Filtering Not Working**
- Verify user branch assignment is correct
- Check if `modula_branch` module is installed
- Ensure employee-branch relationships are set up
- Test with system administrator user

#### **Form Not Saving After Approval**
- Check if form save callbacks are working
- Verify that approval workflow completes successfully
- Check for validation errors in form
- Ensure employee assignment is successful

### **Debug Information**
- Check browser console for JavaScript errors
- Review Odoo server logs for backend errors
- Verify session data in browser developer tools
- Test with different user roles and permissions

### **Performance Issues**
- Monitor employee filtering performance with large datasets
- Check memory usage during employee selection
- Verify database query performance
- Test with multiple concurrent users

## 📚 **Documentation**

### **Technical Documentation**
- **Module Overview**: Complete module architecture and purpose
- **Implementation Analysis**: Detailed technical implementation
- **Testing Instructions**: Comprehensive testing procedures
- **AI Handoff Document**: Development context and guidelines

### **User Documentation**
- **Quick Start Guide**: Basic setup and usage
- **Configuration Guide**: Detailed configuration options
- **Workflow Documentation**: Step-by-step workflow guides
- **Troubleshooting Guide**: Common issues and solutions

### **API Documentation**
- **Template Methods**: Extensibility points and usage
- **Client Actions**: Cross-module integration
- **Session Management**: Session handling patterns
- **UI Components**: Frontend component usage

## 🔮 **Future Enhancements**

### **Short-term Improvements**
- Performance optimization for large employee datasets
- Enhanced security measures for PIN authentication
- Improved user experience and accessibility
- Additional approval workflows

### **Long-term Enhancements**
- Multi-factor authentication support
- Advanced employee management features
- Enhanced integration with other modules
- Cloud-based authentication support

### **Architecture Evolution**
- Microservices architecture for scalability
- API-first design for external integration
- Distributed session management
- Advanced caching and performance optimization

## 📞 **Support**

### **Getting Help**
- Review troubleshooting guide for common issues
- Check documentation for detailed information
- Test with different configurations and scenarios
- Contact development team for complex issues

### **Contributing**
- Follow established coding standards and patterns
- Test changes thoroughly before submission
- Update documentation for new features
- Maintain backward compatibility

### **Reporting Issues**
- Provide detailed error information
- Include steps to reproduce the issue
- Specify environment and configuration details
- Test with minimal configuration to isolate issues

## 📄 **License**

This module is licensed under LGPL-3. See the LICENSE file for details.

## 🏷️ **Version Information**

- **Module Version**: 18.0.1.0.0
- **Odoo Version**: 18.0
- **Dependencies**: web, hr, sale_management, stock, modula_branch
- **License**: LGPL-3

---

**Note**: This module provides a robust foundation for employee selection and validation workflows in Odoo 18. Follow the documentation and guidelines for proper implementation and maintenance.
