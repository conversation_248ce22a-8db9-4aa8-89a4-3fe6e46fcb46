# Generic Employee Selection Widget Design

## 🎯 **Design Overview**

Transform the current hardcoded employee selection system into a **generic, reusable widget** that can be embedded into any model's actions.

## 📋 **Standardized Parameters Structure**

### **ir.actions.client Parameters**
```python
{
    "type": "ir.actions.client",
    "tag": "action_validate_with_employee_selection",
    "params": {
        # Core identification
        "model": "target.model",           # Target model name
        "record_id": 123,                  # Target record ID
        "method_name": "target_method",    # Method to call after employee selection
        
        # Employee selection configuration
        "employee_filter": "all",          # "all", "filtered", "managers_only"
        "dialog_title": "Select Employee", # Custom dialog title
        
        # Context for employee login
        "login_context": {
            "action_type": "validation",    # Type of action being performed
            "res_model": "target.model",    # Target model for context
            "res_id": 123,                  # Target record ID for context
            "custom_field": "value"         # Any custom context fields
        },
        
        # Additional data (model-specific)
        "additional_data": {
            # Any additional data needed by the target method
            "discount_percentage": 10.0,
            "picking_type": "internal",
            # ... other model-specific data
        }
    }
}
```

## 🏗️ **Backend Architecture**

### **1. Base Mixin Class**
```python
class EmployeeValidationMixin(models.AbstractModel):
    _name = 'employee.validation.mixin'
    _description = 'Employee Validation Mixin'
    
    def action_validate_with_employee_selection(self, method_name, **kwargs):
        """Generic method to trigger employee selection for any action"""
        return {
            "type": "ir.actions.client",
            "tag": "action_validate_with_employee_selection",
            "params": {
                "model": self._name,
                "record_id": self.id,
                "method_name": method_name,
                "employee_filter": kwargs.get('employee_filter', 'all'),
                "dialog_title": kwargs.get('dialog_title', 'Select Employee'),
                "login_context": self._get_employee_login_context(**kwargs),
                "additional_data": kwargs.get('additional_data', {}),
            }
        }
    
    def _get_employee_login_context(self, **kwargs):
        """Template method to build login context - override in subclasses"""
        return {
            "action_type": kwargs.get('action_type', 'validation'),
            "res_model": self._name,
            "res_id": self.id,
        }
    
    def action_employee_validation_generic(self, method_name, **kwargs):
        """Generic intermediate method called after employee selection"""
        if not hasattr(self, method_name):
            raise ValidationError(f"Method {method_name} not found on {self._name}")
        
        # Get the method and call it
        method = getattr(self, method_name)
        result = method(**kwargs.get('additional_data', {}))
        
        # Standardize return format
        return self._standardize_validation_result(result)
    
    def _standardize_validation_result(self, result):
        """Standardize return format for all models"""
        if isinstance(result, dict) and result.get('type') == 'ir.actions.act_window':
            return {
                'success': True,
                'wizard_action': result,
                'message': 'Action requires wizard confirmation'
            }
        elif isinstance(result, bool):
            return {
                'success': result,
                'refresh_form': True,
                'message': 'Action completed successfully' if result else 'Action failed'
            }
        elif isinstance(result, dict):
            # Already in correct format
            return result
        else:
            return {
                'success': True,
                'refresh_form': True,
                'message': 'Action completed'
            }
```

### **2. Model Implementation Example**
```python
class StockPicking(models.Model):
    _inherit = ["stock.picking", "employee.validation.mixin"]
    
    def action_validate_with_employee_selection(self):
        """Specific implementation for stock picking"""
        return super().action_validate_with_employee_selection(
            method_name="button_validate",
            employee_filter="all",
            dialog_title="Select Employee for Stock Picking Validation",
            action_type="stock_validation",
            additional_data={
                "picking_type": self.picking_type_id.name,
                "move_lines": self.move_ids.read(['id', 'product_id', 'quantity'])
            }
        )
    
    def _get_employee_login_context(self, **kwargs):
        """Override to add stock-specific context"""
        context = super()._get_employee_login_context(**kwargs)
        context.update({
            "action_validate_with_employee_selection": True,
            "picking_type": kwargs.get('additional_data', {}).get('picking_type')
        })
        return context
```

## 🎨 **Frontend Architecture**

### **Generic JavaScript Handler**
```javascript
export async function ActionValidateWithEmployeeSelection(env, action) {
    const params = action.params;
    
    try {
        // 1. Check if employee selection is needed (optional pre-check)
        if (params.pre_check_method) {
            const needsSelection = await env.services.orm.call(
                params.model,
                params.pre_check_method,
                [params.record_id],
                { context: params.additional_data }
            );
            
            if (!needsSelection) {
                return await executeTargetMethod(env, params);
            }
        }
        
        // 2. Load employees based on filter
        const employees = await loadEmployees(env, params.employee_filter);
        
        // 3. Show employee selection dialog
        await showEmployeeSelectionDialog(env, params, employees);
        
    } catch (error) {
        console.error("Error in generic employee validation:", error);
        env.services.notification.add("Error in employee validation: " + error.message, { type: "danger" });
    }
}

const executeTargetMethod = async (env, params) => {
    const result = await env.services.orm.call(
        params.model,
        "action_employee_validation_generic",
        [params.record_id],
        {
            context: {
                method_name: params.method_name,
                additional_data: params.additional_data
            }
        }
    );
    
    return handleStandardizedResult(env, params, result);
};

const handleStandardizedResult = async (env, params, result) => {
    env.services.dialog.closeAll();
    
    if (result.success) {
        if (result.wizard_action) {
            // Handle wizard action
            await env.services.action.doAction(result.wizard_action, {
                onClose: () => refreshRecord(env, params)
            });
        } else if (result.refresh_form) {
            // Refresh the form
            await refreshRecord(env, params);
        }
        
        if (result.message) {
            env.services.notification.add(result.message, { type: "success" });
        }
    } else {
        env.services.notification.add(result.message || "Action failed", { type: "danger" });
    }
};
```

## 🔄 **Migration Strategy**

### **Phase 1: Create Base Infrastructure**
1. Create `EmployeeValidationMixin` abstract model
2. Create generic JavaScript handler
3. Update existing models to inherit from mixin

### **Phase 2: Backward Compatibility**
1. Keep existing methods as wrappers
2. Gradually migrate to generic system
3. Maintain all current functionality

### **Phase 3: New Model Integration**
1. Any model can inherit `EmployeeValidationMixin`
2. Override `_get_employee_login_context()` if needed
3. Call `action_validate_with_employee_selection()` with appropriate parameters

## 🎯 **Benefits**

### **For Developers**
- **Reusable**: One system works for all models
- **Consistent**: Standardized parameters and return formats
- **Extensible**: Easy to add new models
- **Maintainable**: Single codebase for employee selection logic

### **For Users**
- **Consistent UX**: Same employee selection experience across all modules
- **Reliable**: Standardized error handling and validation
- **Flexible**: Configurable dialogs and employee filtering

## 📝 **Usage Examples**

### **Purchase Order Example**
```python
class PurchaseOrder(models.Model):
    _inherit = ["purchase.order", "employee.validation.mixin"]
    
    def action_approve_with_employee_selection(self):
        return super().action_validate_with_employee_selection(
            method_name="button_approve",
            employee_filter="managers_only",
            dialog_title="Select Manager for Purchase Approval",
            action_type="purchase_approval"
        )
```

### **Account Move Example**
```python
class AccountMove(models.Model):
    _inherit = ["account.move", "employee.validation.mixin"]
    
    def action_post_with_employee_selection(self):
        return super().action_validate_with_employee_selection(
            method_name="action_post",
            employee_filter="filtered",
            dialog_title="Select Employee for Journal Entry Posting",
            action_type="journal_posting"
        )
```
