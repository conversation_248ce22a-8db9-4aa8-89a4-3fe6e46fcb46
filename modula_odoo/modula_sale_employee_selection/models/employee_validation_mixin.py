# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.exceptions import ValidationError
from odoo.http import request


class EmployeeValidationMixin(models.AbstractModel):
    """
    Generic mixin for employee validation workflows.
    
    This mixin provides a standardized way to implement employee selection
    and validation for any model's actions. Models can inherit this mixin
    and use the generic employee selection widget.
    """
    _name = 'employee.validation.mixin'
    _description = 'Employee Validation Mixin'

    def action_validate_with_employee_selection(self, method_name, **kwargs):
        """
        Generic method to trigger employee selection for any action.
        
        Args:
            method_name (str): Name of the method to call after employee selection
            **kwargs: Additional configuration options
                - employee_filter (str): 'all', 'filtered', 'managers_only'
                - dialog_title (str): Custom dialog title
                - action_type (str): Type of action being performed
                - additional_data (dict): Additional data for the target method
                - pre_check_method (str): Optional method to check if selection is needed
        
        Returns:
            dict: ir.actions.client action configuration
        """
        self.ensure_one()
        
        # Validate that the target method exists
        if not hasattr(self, method_name):
            raise ValidationError(f"Method '{method_name}' not found on model '{self._name}'")
        
        return {
            "type": "ir.actions.client",
            "tag": "action_validate_with_employee_selection",
            "params": {
                "model": self._name,
                "record_id": self.id,
                "method_name": method_name,
                "employee_filter": kwargs.get('employee_filter', 'all'),
                "dialog_title": kwargs.get('dialog_title', 'Select Employee'),
                "login_context": self._get_employee_login_context(**kwargs),
                "additional_data": kwargs.get('additional_data', {}),
                "pre_check_method": kwargs.get('pre_check_method'),
            }
        }

    def _get_employee_login_context(self, **kwargs):
        """
        Template method to build login context for employee authentication.
        
        Override this method in subclasses to add model-specific context.
        
        Args:
            **kwargs: Configuration options from action_validate_with_employee_selection
        
        Returns:
            dict: Context dictionary for employee login
        """
        return {
            "action_type": kwargs.get('action_type', 'validation'),
            "res_model": self._name,
            "res_id": self.id,
            "action_validate_with_employee_selection": True,
        }

    def action_employee_validation_generic(self, method_name, additional_data=None):
        """
        Generic intermediate method called after employee selection.
        
        This method is called by the JavaScript client action after successful
        employee authentication. It executes the target method and standardizes
        the return format.
        
        Args:
            method_name (str): Name of the method to execute
            additional_data (dict): Additional data to pass to the method
        
        Returns:
            dict: Standardized result format
        """
        self.ensure_one()
        
        try:
            # Validate employee session
            if not request.session.get("session_owner", False):
                return {
                    'success': False,
                    'error': 'No employee session found',
                    'message': 'Please select an employee before proceeding.'
                }
            
            # Validate that the target method exists
            if not hasattr(self, method_name):
                return {
                    'success': False,
                    'error': f'Method {method_name} not found',
                    'message': f'Method "{method_name}" not found on model "{self._name}"'
                }
            
            # Get the method and prepare arguments
            method = getattr(self, method_name)
            method_kwargs = additional_data or {}
            
            # Execute the target method
            result = method(**method_kwargs)
            
            # Standardize the return format
            return self._standardize_validation_result(result, method_name)
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'Error executing {method_name}: {str(e)}'
            }

    def _standardize_validation_result(self, result, method_name):
        """
        Standardize return format for all models.
        
        This ensures consistent return format regardless of what the target
        method returns (boolean, dict, wizard action, etc.).
        
        Args:
            result: The result returned by the target method
            method_name (str): Name of the executed method
        
        Returns:
            dict: Standardized result format
        """
        if isinstance(result, dict) and result.get('type') == 'ir.actions.act_window':
            # Result is a wizard action (like backorder wizard)
            return {
                'success': True,
                'wizard_action': result,
                'message': f'{method_name} requires additional confirmation'
            }
        elif isinstance(result, bool):
            # Result is a boolean (simple success/failure)
            return {
                'success': result,
                'refresh_form': True,
                'message': f'{method_name} completed successfully' if result else f'{method_name} failed'
            }
        elif isinstance(result, dict):
            # Result is already a dictionary - check if it's in our standard format
            if 'success' in result:
                # Already in standard format
                return result
            else:
                # Convert to standard format
                return {
                    'success': True,
                    'wizard_action': result if result.get('type') else None,
                    'refresh_form': not bool(result.get('type')),
                    'message': result.get('message', f'{method_name} completed'),
                    'additional_data': result
                }
        else:
            # Unknown result type - assume success
            return {
                'success': True,
                'refresh_form': True,
                'message': f'{method_name} completed',
                'result_data': result
            }

    def _get_employee_filter_domain(self, filter_type='all'):
        """
        Get domain for filtering employees based on filter type.
        
        Override this method in subclasses to implement custom filtering logic.
        
        Args:
            filter_type (str): Type of filter ('all', 'filtered', 'managers_only')
        
        Returns:
            list: Domain for filtering employees
        """
        if filter_type == 'managers_only':
            # Example: filter for managers only
            return [('job_id.name', 'ilike', 'manager')]
        elif filter_type == 'filtered':
            # Example: filter for employees with is_show=True
            return [('is_show', '=', True)]
        else:
            # Return all employees
            return []

    def _need_employee_selection(self, method_name, **kwargs):
        """
        Template method to determine if employee selection is needed.
        
        Override this method in subclasses to implement custom logic
        for when employee selection should be required.
        
        Args:
            method_name (str): Name of the method being executed
            **kwargs: Additional context
        
        Returns:
            bool: True if employee selection is needed, False otherwise
        """
        # Default: always require employee selection
        return True

    def _get_employee_validation_title(self, method_name, **kwargs):
        """
        Template method to generate dialog title.
        
        Override this method in subclasses to provide custom dialog titles.
        
        Args:
            method_name (str): Name of the method being executed
            **kwargs: Additional context
        
        Returns:
            str: Dialog title
        """
        return f"Select Employee for {method_name.replace('_', ' ').title()}"

    def _post_employee_validation(self, method_name, result, **kwargs):
        """
        Template method called after successful employee validation.
        
        Override this method in subclasses to implement post-validation logic
        like logging, notifications, or additional processing.
        
        Args:
            method_name (str): Name of the executed method
            result (dict): Standardized result from the method execution
            **kwargs: Additional context
        """
        # Default: no additional processing
        pass
