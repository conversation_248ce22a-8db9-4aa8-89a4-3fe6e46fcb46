# -*- coding: utf-8 -*-
from odoo.http import request
from odoo import api, fields, models

from odoo.exceptions import ValidationError

class StockPicking(models.Model):
    _inherit = ["stock.picking", "employee.validation.mixin"]

    employee_id = fields.Many2one(
        "hr.employee",
        string="Employee",
    )

    def button_validate(self):
        if not self.env.context.get('skip_backorder'):
            if not request.session.get("session_owner", False):
                raise ValidationError(self.env._("Please select an employee before validating the delivery order."))
            else:
                self.employee_id = request.session.get("session_owner", False)
                request.session["session_owner"] = False
        return super(StockPicking, self).button_validate()

    def _create_backorder(self, backorder_moves=None):
        self = self.with_context(erase_employee_id=True)
        return super()._create_backorder(backorder_moves)

    def copy(self, default=None):
        if self.env.context.get('erase_employee_id'):
            default['employee_id'] = None
        return super().copy(default)

    def action_validate_with_employee_selection(self):
        """Use the generic employee selection system for stock picking validation"""
        return super().action_validate_with_employee_selection(
            method_name="button_validate",
            employee_filter="all",
            dialog_title="Select Employee for Stock Picking Validation",
            action_type="stock_validation",
            additional_data={
                "picking_state": self.state,
                "picking_name": self.name,
                "picking_type": self.picking_type_id.name,
                "move_lines": self.move_ids.read(['id', 'product_id', 'quantity', 'product_uom_qty']),
            }
        )

    def _get_employee_login_context(self, **kwargs):
        """Override to add stock-specific context"""
        context = super()._get_employee_login_context(**kwargs)
        context.update({
            "action_validate_with_employee_selection": True,
            "picking_type": kwargs.get('additional_data', {}).get('picking_type'),
            "picking_state": kwargs.get('additional_data', {}).get('picking_state'),
        })
        return context

    def action_employee_validation_for_stock_picking(self):
        """
        LEGACY METHOD: Kept for backward compatibility.
        New implementations should use the generic system.
        """
        return self.action_employee_validation_generic("button_validate")