# -*- coding: utf-8 -*-
from odoo.http import request
from odoo.exceptions import ValidationError

from odoo import api, fields, models, _


class SaleOrder(models.Model):
    _inherit = ["sale.order", "employee.validation.mixin"]

    need_approve = fields.Boolean(string="Need Approve", default=False)

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if request.session.get("session_owner", False):
                vals["employee_id"] = request.session.get("session_owner", False)
        request.session['session_owner'] = False
        records = super(SaleOrder, self).create(vals_list)
        return records

    def write(self, vals):
        if vals.get("employee_id"):
            request.session['session_owner'] = False
        return super(SaleOrder, self).write(vals)

    def need_employee_selection(self, **kwargs):
        return False

    def action_approve_sale_order(self):
        return {
            'type': 'ir.actions.act_window_close',
        }

    def action_employee_validation_for_sale_order(self):
        """
        LEGACY METHOD: Kept for backward compatibility.
        New implementations should use the generic system.
        """
        return self.action_employee_validation_generic("action_confirm")

    # Example: Generic employee selection for order confirmation
    def action_confirm_with_employee_selection(self):
        """Example: Require employee selection before confirming sale order"""
        return super().action_validate_with_employee_selection(
            method_name="action_confirm",
            employee_filter="managers_only",
            dialog_title="Select Manager for Sale Order Confirmation",
            action_type="sale_confirmation",
            additional_data={
                "order_amount": self.amount_total,
                "customer": self.partner_id.name,
                "order_lines": self.order_line.read(['id', 'product_id', 'price_unit', 'product_uom_qty']),
            }
        )

    # Example: Generic employee selection for discount approval
    def action_approve_discount_with_employee_selection(self, discount_percentage=0.0):
        """Example: Require manager approval for discounts above threshold"""
        return super().action_validate_with_employee_selection(
            method_name="apply_discount",
            employee_filter="managers_only",
            dialog_title="Select Manager for Discount Approval",
            action_type="discount_approval",
            pre_check_method="need_employee_selection",
            additional_data={
                "discount_percentage": discount_percentage,
                "order_amount": self.amount_total,
                "customer": self.partner_id.name,
            }
        )

    def apply_discount(self, discount_percentage=0.0):
        """Example method that applies discount to order lines"""
        self.ensure_one()
        for line in self.order_line:
            line.discount = discount_percentage
        return True

    def _get_employee_login_context(self, **kwargs):
        """Override to add sale-specific context"""
        context = super()._get_employee_login_context(**kwargs)
        context.update({
            "action_approve_sale_order": kwargs.get('action_type') == 'discount_approval',
            "action_validate_with_employee_selection": True,
            "order_amount": kwargs.get('additional_data', {}).get('order_amount'),
            "customer_id": self.partner_id.id,
        })
        return context

    def _need_employee_selection(self, method_name, **kwargs):
        """Override to implement custom logic for when employee selection is needed"""
        if method_name == "apply_discount":
            discount = kwargs.get('additional_data', {}).get('discount_percentage', 0.0)
            # Require employee selection for discounts > 5%
            return discount > 5.0
        elif method_name == "action_confirm":
            # Require employee selection for orders > $1000
            return self.amount_total > 1000.0

        # Default: always require employee selection
        return super()._need_employee_selection(method_name, **kwargs)
