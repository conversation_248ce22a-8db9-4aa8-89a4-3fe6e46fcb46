# -*- coding: utf-8 -*-
from odoo import api, fields, models
from odoo.exceptions import ValidationError

class PurchaseOrder(models.Model):
    """
    EXAMPLE: This demonstrates how to add generic employee selection
    to any model. This is just an example and not meant for production use.
    
    To use this example:
    1. Add 'purchase' to module dependencies in __manifest__.py
    2. Add this file to models/__init__.py
    3. Install/upgrade the module
    """
    _inherit = ["purchase.order", "employee.validation.mixin"]

    # Example: Generic employee selection for purchase approval
    def action_approve_with_employee_selection(self):
        """Example: Require manager approval for purchase orders above threshold"""
        return super().action_validate_with_employee_selection(
            method_name="button_approve",
            employee_filter="managers_only",
            dialog_title="Select Manager for Purchase Order Approval",
            action_type="purchase_approval",
            pre_check_method="need_employee_selection",
            additional_data={
                "order_amount": self.amount_total,
                "vendor": self.partner_id.name,
                "order_lines": self.order_line.read(['id', 'product_id', 'price_unit', 'product_qty']),
            }
        )

    # Example: Generic employee selection for order confirmation
    def action_confirm_with_employee_selection(self):
        """Example: Require employee selection before confirming purchase order"""
        return super().action_validate_with_employee_selection(
            method_name="button_confirm",
            employee_filter="all",
            dialog_title="Select Employee for Purchase Order Confirmation",
            action_type="purchase_confirmation",
            additional_data={
                "order_amount": self.amount_total,
                "vendor": self.partner_id.name,
            }
        )

    # Example: Generic employee selection for receiving
    def action_receive_with_employee_selection(self):
        """Example: Require employee selection for receiving goods"""
        return super().action_validate_with_employee_selection(
            method_name="action_create_invoice",
            employee_filter="all",
            dialog_title="Select Employee for Goods Receipt",
            action_type="goods_receipt",
            additional_data={
                "order_amount": self.amount_total,
                "vendor": self.partner_id.name,
                "receipt_date": fields.Datetime.now(),
            }
        )

    def _get_employee_login_context(self, **kwargs):
        """Override to add purchase-specific context"""
        context = super()._get_employee_login_context(**kwargs)
        context.update({
            "action_validate_with_employee_selection": True,
            "order_amount": kwargs.get('additional_data', {}).get('order_amount'),
            "vendor_id": self.partner_id.id,
            "purchase_action_type": kwargs.get('action_type'),
        })
        return context

    def _need_employee_selection(self, method_name, **kwargs):
        """Override to implement custom logic for when employee selection is needed"""
        if method_name == "button_approve":
            # Require manager approval for orders > $5000
            return self.amount_total > 5000.0
        elif method_name == "button_confirm":
            # Require employee selection for orders > $1000
            return self.amount_total > 1000.0
        elif method_name == "action_create_invoice":
            # Always require employee selection for receiving
            return True
        
        # Default: always require employee selection
        return super()._need_employee_selection(method_name, **kwargs)

    def _get_employee_validation_title(self, method_name, **kwargs):
        """Override to provide custom dialog titles"""
        titles = {
            "button_approve": f"Approve Purchase Order {self.name} (${self.amount_total:,.2f})",
            "button_confirm": f"Confirm Purchase Order {self.name}",
            "action_create_invoice": f"Receive Goods for PO {self.name}",
        }
        return titles.get(method_name, super()._get_employee_validation_title(method_name, **kwargs))

    def _post_employee_validation(self, method_name, result, **kwargs):
        """Override to implement post-validation logic"""
        super()._post_employee_validation(method_name, result, **kwargs)
        
        # Example: Log the validation
        if result.get('success'):
            self.message_post(
                body=f"Employee validation completed for {method_name} by employee ID: {self.env.context.get('employee_id', 'Unknown')}",
                message_type='notification'
            )

# Example XML button that would use the generic system:
"""
<record id="view_purchase_order_form_employee_selection" model="ir.ui.view">
    <field name="name">purchase.order.form.employee.selection</field>
    <field name="model">purchase.order</field>
    <field name="inherit_id" ref="purchase.purchase_order_form"/>
    <field name="arch" type="xml">
        <xpath expr="//header" position="inside">
            <!-- Example: Manager approval button -->
            <button name="action_approve_with_employee_selection" 
                    type="object" 
                    string="Approve with Employee Selection"
                    class="btn-primary"
                    attrs="{'invisible': [('state', '!=', 'to approve')]}"/>
            
            <!-- Example: Confirmation with employee selection -->
            <button name="action_confirm_with_employee_selection" 
                    type="object" 
                    string="Confirm with Employee Selection"
                    class="btn-secondary"
                    attrs="{'invisible': [('state', '!=', 'draft')]}"/>
            
            <!-- Example: Receiving with employee selection -->
            <button name="action_receive_with_employee_selection" 
                    type="object" 
                    string="Receive with Employee Selection"
                    class="btn-secondary"
                    attrs="{'invisible': [('state', '!=', 'purchase')]}"/>
        </xpath>
    </field>
</record>
"""

# Example usage in other modules:
"""
# In your custom module, you can easily add employee selection to any action:

class MyCustomModel(models.Model):
    _inherit = ["my.custom.model", "employee.validation.mixin"]
    
    def my_custom_action_with_employee_selection(self):
        return super().action_validate_with_employee_selection(
            method_name="my_custom_method",
            employee_filter="managers_only",  # or "all" or "filtered"
            dialog_title="Select Employee for Custom Action",
            action_type="custom_action",
            additional_data={
                "custom_field": self.custom_field,
                "amount": self.amount,
            }
        )
    
    def my_custom_method(self, **kwargs):
        # Your custom business logic here
        return True  # or return a wizard action dict
"""
