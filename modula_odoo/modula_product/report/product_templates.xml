<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <data>
        <template id="label_product_product_view_modula" inherit_id="stock.label_product_product_view">
            <xpath expr="." position="replace">
                <t t-foreach="quantity.items()" t-as="barcode_and_qty_by_product">
                    <t t-set="product" t-value="barcode_and_qty_by_product[0]"/>
                    <t t-foreach="barcode_and_qty_by_product[1]" t-as="product_info">
                        <t t-set="barcode" t-value="product_info['barcode']"/>
                        <t t-set="currency_id" t-value="pricelist.currency_id or product.currency_id"/>
                        <t t-foreach="range(product_info['quantity'])" t-as="qty">
                            <t t-translation="off">
^XA^CI28
^PW319
^LL567

<!-- Product Code  -->
<t t-if="product.default_code">
^FT18,25^A0N,18,14^FD[<t t-out="product_info['default_code'][0]"/>]^FS
</t>

<!-- Product Name -->
^FT18,50^A0N,20,17^FD<t t-out="product_info['display_name_markup']"/>^FS

<!-- Price with Tax -->
<t t-if="price_included">
^FO18,65
<t t-if="currency_id.position == 'after'">
^A0N,45,55^FH^FD<t t-esc="pricelist._get_product_price(product, 1, currency_id)" t-options='{"widget": "float", "precision": 2}'/><t t-esc="currency_id.symbol"/>^FS
</t>
<t t-if="currency_id.position == 'before'">
^A0N,45,55^FH^FD<t t-esc="currency_id.symbol"/><t t-esc="pricelist._get_product_price(product, 1, currency_id)" t-options='{"widget": "float", "precision": 2}'/>^FS
</t>
</t>

<!-- Barcode -->
<t t-if="barcode">
^FO18,110^BY1
^BCN,80,N,N,N
^FD<t t-out="barcode"/>^FS
</t>

^FT18,210
^FA310,1,0,C
^A0N,18,16
^FD<t t-out="barcode"/>^FS

^XZ
                            </t>
                        </t>
                    </t>
                </t>
            </xpath>
        </template>

        <template id="report_simple_label2x7_modula" inherit_id="product.report_simple_label2x7">
            <xpath expr="//strong[hasclass('o_label_price')]" position="replace">
                <strong class="o_label_price" t-out="pricelist._get_product_price_with_tax(product, 1, pricelist.currency_id or product.currency_id)"
                                    t-options="{'widget': 'monetary', 'display_currency': pricelist.currency_id or product.currency_id, 'label_price': True}"/>
            </xpath>
        </template>

        <template id="report_simple_label4x7_modula" inherit_id="product.report_simple_label4x7">
            <xpath expr="//strong[hasclass('o_label_price_medium')]" position="replace">
                <strong class="o_label_price_medium" t-out="pricelist._get_product_price_with_tax(product, 1, pricelist.currency_id or product.currency_id)"
                                t-options="{'widget': 'monetary', 'display_currency': pricelist.currency_id or product.currency_id, 'label_price': True}"/>
            </xpath>
        </template>

        <template id="report_simple_label4x12_modula" inherit_id="product.report_simple_label4x12">
            <xpath expr="//div[hasclass('o_label_price_medium')]" position="replace">
                <div class="o_label_price_medium text-end">
                    <strong t-out="pricelist._get_product_price_with_tax(product, 1, pricelist.currency_id or product.currency_id)"
                        t-options="{'widget': 'monetary', 'display_currency': pricelist.currency_id or product.currency_id, 'label_price': True}"/>
                </div>
            </xpath>
        </template>

        <template id="report_simple_label_dymo_modula" inherit_id="product.report_simple_label_dymo">
            <xpath expr="//strong[hasclass('o_label_price_small')]" position="replace">
                <strong class="o_label_price_small" t-out="pricelist._get_product_price_with_tax(product, 1, pricelist.currency_id or product.currency_id)"
                                t-options="{'widget': 'monetary', 'display_currency': pricelist.currency_id or product.currency_id, 'label_price': True}"/>
            </xpath>
        </template>

    </data>
</odoo>
